#!/usr/bin/env python
"""
Script to register OneSignal player ID for appleuser
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bopmaps.settings')
django.setup()

from django.contrib.auth import get_user_model
from notifications.models import OneSignalPlayerID
from notifications.services import onesignal_service

User = get_user_model()

def register_player_id():
    """Register a OneSignal player ID for appleuser"""
    
    # Get the appleuser
    try:
        user = User.objects.get(id=300)  # appleuser ID
        print(f"✅ Found user: {user.username} (ID: {user.id})")
    except User.DoesNotExist:
        print("❌ User with ID 300 not found!")
        return
    
    # You need to provide your actual OneSignal player ID from your device
    # This is typically obtained from the OneSignal SDK in your mobile app
    player_id = input("Enter your OneSignal Player ID (from your mobile device): ").strip()
    
    if not player_id:
        print("❌ No player ID provided!")
        return
    
    platform = input("Enter platform (ios/android/web) [default: ios]: ").strip() or "ios"
    
    # Register the player ID
    try:
        success = onesignal_service.register_player(
            user=user,
            player_id=player_id,
            platform=platform
        )
        
        if success:
            print(f"✅ Successfully registered OneSignal player ID!")
            print(f"   Player ID: {player_id}")
            print(f"   Platform: {platform}")
            print(f"   User: {user.username}")
            
            # Verify registration
            players = OneSignalPlayerID.objects.filter(user=user, is_active=True)
            print(f"\n📱 Active player IDs for {user.username}:")
            for player in players:
                print(f"   - {player.platform}: {player.player_id}")
                
        else:
            print("❌ Failed to register player ID!")
            
    except Exception as e:
        print(f"❌ Error registering player ID: {str(e)}")

def list_existing_players():
    """List existing OneSignal player IDs for appleuser"""
    try:
        user = User.objects.get(id=300)
        players = OneSignalPlayerID.objects.filter(user=user)
        
        print(f"📱 OneSignal Player IDs for {user.username}:")
        if players.exists():
            for player in players:
                status = "✅ Active" if player.is_active else "❌ Inactive"
                print(f"   - {player.platform}: {player.player_id} ({status})")
        else:
            print("   No player IDs registered")
            
    except User.DoesNotExist:
        print("❌ User not found!")

if __name__ == "__main__":
    print("🔔 OneSignal Player ID Registration")
    print("=" * 40)
    
    print("\n1. List existing player IDs")
    list_existing_players()
    
    print("\n2. Register new player ID")
    print("To get your OneSignal Player ID:")
    print("- Open your BOP Maps app")
    print("- Check the app logs or developer console")
    print("- Look for OneSignal player ID in the initialization")
    print("- Or use OneSignal's test tools")
    
    choice = input("\nDo you want to register a new player ID? (y/n): ").strip().lower()
    if choice == 'y':
        register_player_id()
    else:
        print("Skipping registration.")

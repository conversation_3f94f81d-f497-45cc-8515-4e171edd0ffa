from django.utils import timezone
from django.db.models import Q
from collections import defaultdict, Counter
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import math

from ..models import Achievement, UserAchievement
from pins.models import Pin


class ProgressTracker:
    """
    Service for tracking and updating user achievement progress
    """
    
    @staticmethod
    def update_pin_progress(user, pin_data: Dict[str, Any]):
        """
        Update progress for all relevant achievements when a user creates a pin
        
        Args:
            user: User instance
            pin_data: Dictionary containing pin information:
                - latitude, longitude: location coordinates
                - artist_id, artist_name: artist information  
                - genre: music genre
                - album_id, album_name: album information
                - track_id, track_name: track information
                - created_at: pin creation timestamp
                - is_public: whether the pin is public
                - city, state, country, continent: location details
                - population: city population (if available)
        """
        if not pin_data.get('is_public', True):
            return  # Most challenges require public pins only
        
        # Get or create user achievements for all active achievements
        achievements = Achievement.objects.all()
        
        for achievement in achievements:
            user_achievement, created = UserAchievement.objects.get_or_create(
                user=user,
                achievement=achievement,
                defaults={'progress': {}}
            )
            
            # Update progress based on achievement criteria
            ProgressTracker._update_pin_criteria(user_achievement, pin_data)
    
    @staticmethod
    def _update_pin_criteria(user_achievement: UserAchievement, pin_data: Dict[str, Any]):
        """Update progress for pin-related criteria"""
        criteria = user_achievement.achievement.criteria
        progress = user_achievement.progress or {}
        
        # === BASIC COUNTS ===
        if 'total_pins' in criteria:
            progress['total_pins'] = progress.get('total_pins', 0) + 1
        
        # === GEOGRAPHIC TRACKING ===
        if any(key in criteria for key in ['pins_in_radius', 'different_cities', 'different_states', 
                                          'different_countries', 'different_continents', 'districts',
                                          'continents_required']):
            ProgressTracker._update_geographic_progress(progress, pin_data, criteria)
        
        # === ARTIST TRACKING ===
        if any(key in criteria for key in ['unique_artists', 'same_artist_pins', 'unique_albums']):
            ProgressTracker._update_artist_progress(progress, pin_data, criteria)
        
        # === GENRE TRACKING ===
        if any(key in criteria for key in ['unique_genres', 'same_genre_pins', 'unique_genres_in_city', 
                                          'same_genre_different_countries', 'different_genres_same_day',
                                          'unique_genres_weekend', 'genre_patterns']):
            ProgressTracker._update_genre_progress(progress, pin_data, criteria)
        
        # === TIME-BASED TRACKING ===
        if any(key in criteria for key in ['different_days', 'time_range', 'consecutive_days']):
            ProgressTracker._update_time_progress(progress, pin_data, criteria)
        
        # === SPECIAL CONDITIONS ===
        if 'no_prior_pins' in criteria:
            # Check if this location had no prior pins
            progress['no_prior_pins_verified'] = pin_data.get('is_first_in_location', False)
        
        if 'night_pins' in criteria:
            # Check if pin was created between midnight and 4 AM
            created_time = pin_data.get('created_at', timezone.now())
            if isinstance(created_time, str):
                created_time = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
            hour = created_time.hour
            if 0 <= hour < 4:
                progress['night_pins'] = progress.get('night_pins', 0) + 1
        
        if 'max_population' in criteria:
            # Track pins in small towns
            population = pin_data.get('population', 0)
            if population is not None and population > 0 and population < criteria['max_population']:
                progress['small_town_pins'] = progress.get('small_town_pins', 0) + 1
        
        # Save updated progress
        user_achievement.progress = progress
        user_achievement.save()
        user_achievement.check_completion()
    
    @staticmethod
    def _update_geographic_progress(progress: Dict, pin_data: Dict, criteria: Dict):
        """Update geographic-related progress"""
        latitude = pin_data.get('latitude')
        longitude = pin_data.get('longitude')
        city = pin_data.get('city')
        state = pin_data.get('state') 
        country = pin_data.get('country')
        continent = pin_data.get('continent')
        district = pin_data.get('district')
        population = pin_data.get('population')
        
        # Helper function to validate location values
        def is_valid_location_value(value):
            if not value:
                return False
            if isinstance(value, str):
                cleaned = value.strip().lower()
                if cleaned in ['unknown', 'none', 'null', '', 'n/a', 'na']:
                    return False
            return True
        
        # Track unique locations at all levels - but only valid ones
        if is_valid_location_value(city):
            cities = set(progress.get('cities_visited', []))
            cities.add(city)
            progress['cities_visited'] = list(cities)
            progress['different_cities'] = len(cities)
        
        if is_valid_location_value(state):
            states = set(progress.get('states_visited', []))
            states.add(state)
            progress['states_visited'] = list(states)
            progress['different_states'] = len(states)
        
        if is_valid_location_value(country):
            countries = set(progress.get('countries_visited', []))
            countries.add(country)
            progress['countries_visited'] = list(countries)
            progress['different_countries'] = len(countries)
        
        if is_valid_location_value(continent):
            continents = set(progress.get('visited_continents', []))
            continents.add(continent)
            progress['visited_continents'] = list(continents)
            progress['different_continents'] = len(continents)
        
        if is_valid_location_value(district):
            districts = set(progress.get('districts_visited', []))
            districts.add(district)
            progress['districts_visited'] = list(districts)
            progress['different_districts'] = len(districts)
        
        # Track pins in radius (for challenges like "Neighborhood Navigator")
        if latitude and longitude:
            pin_locations = progress.get('pin_locations', [])
            pin_locations.append([latitude, longitude])
            progress['pin_locations'] = pin_locations
            
            # Calculate pins within different radius requirements
            if 'pins_in_radius' in criteria:
                radius_km = criteria.get('radius_km', 1)
                pins_in_radius = ProgressTracker._count_pins_in_radius(
                    pin_locations, radius_km
                )
                progress['pins_in_radius'] = pins_in_radius
            
            # Calculate maximum distance between pins
            if 'max_distance_km' in criteria:
                max_distance = ProgressTracker._calculate_max_distance(pin_locations)
                progress['max_distance_km'] = max_distance
        
        # Track population-based challenges
        if population is not None:
            # Small town tracking (population < 50,000)
            if population < 50000:
                small_town_pins = progress.get('small_town_pins', 0)
                progress['small_town_pins'] = small_town_pins + 1
            
            # Big city tracking (population > 1,000,000)
            if population > 1000000:
                big_city_pins = progress.get('big_city_pins', 0)
                progress['big_city_pins'] = big_city_pins + 1
        
        # Track time-based location challenges
        created_at = pin_data.get('created_at')
        time_of_day = pin_data.get('time_of_day')
        
        if created_at and time_of_day is not None:
            # Midnight pins (0-4 AM)
            if 0 <= time_of_day < 4:
                midnight_pins = progress.get('midnight_pins', 0)
                progress['midnight_pins'] = midnight_pins + 1
            
            # Weekend pins
            if created_at.weekday() >= 5:  # Saturday=5, Sunday=6
                weekend_pins = progress.get('weekend_pins', 0)
                progress['weekend_pins'] = weekend_pins + 1
        
        # Track total pins for basic counting
        total_pins = progress.get('total_pins', 0)
        progress['total_pins'] = total_pins + 1
    
    @staticmethod
    def _update_artist_progress(progress: Dict, pin_data: Dict, criteria: Dict):
        """Update artist-related progress"""
        artist_id = pin_data.get('artist_id')
        artist_name = pin_data.get('artist_name') or pin_data.get('track_artist')  # Fix: also check track_artist
        album_id = pin_data.get('album_id')
        album_name = pin_data.get('album_name')
        city = pin_data.get('city')
        
        # Fix: Properly check for valid artist data (not None, empty, or "None" string)
        if artist_id or (artist_name and artist_name not in ['None', '', 'null', 'unknown']):
            artist_key = artist_id or artist_name
            
            # Track unique artists
            artists = set(progress.get('artists_played', []))
            artists.add(artist_key)
            progress['artists_played'] = list(artists)
            progress['unique_artists'] = len(artists)
            
            # Track pins by same artist
            artist_pins = progress.get('artist_pin_counts', {})
            artist_pins[artist_key] = artist_pins.get(artist_key, 0) + 1
            progress['artist_pin_counts'] = artist_pins
            
            # Update max pins for same artist
            progress['same_artist_pins'] = max(artist_pins.values()) if artist_pins else 0
            
            # NEW: Track artists per city for "Artist Variety Master" challenge
            if city and 'unique_artists_in_city' in criteria:
                city_artists = progress.get('city_artists', {})
                if city not in city_artists:
                    city_artists[city] = []
                
                # Convert to set for operations, then back to list
                artist_set = set(city_artists[city])
                artist_set.add(artist_key)
                city_artists[city] = list(artist_set)
                
                progress['city_artists'] = city_artists
                
                # Update max unique artists in any single city
                max_artists_in_city = max(
                    len(artists) for artists in city_artists.values()
                ) if city_artists else 0
                progress['unique_artists_in_city'] = max_artists_in_city
            
            # NEW: Check if this is the first artist in city for "Artist Pioneer" challenge
            if city and 'first_artist_in_city' in criteria:
                # Check if this artist has been pinned in this city before by ANY user
                existing_pins = Pin.objects.filter(
                    track_artist=artist_key,
                    location_name__icontains=city,
                    is_private=False
                ).exclude(
                    id=pin_data.get('current_pin_id')  # Exclude current pin
                ).exists()
                
                if not existing_pins:
                    # This user is the first to pin this artist in this city!
                    progress['first_artist_in_city'] = True
            
            # Track unique albums for same artist
            if album_id or album_name:
                album_key = album_id or album_name
                artist_albums = progress.get('artist_albums', {})
                if artist_key not in artist_albums:
                    artist_albums[artist_key] = []
                
                # Convert to set for operations, then back to list
                album_set = set(artist_albums[artist_key])
                album_set.add(album_key)
                artist_albums[artist_key] = list(album_set)
                
                progress['artist_albums'] = artist_albums
                
                # Update max albums for same artist
                max_albums = max(
                    len(albums) for albums in artist_albums.values()
                ) if artist_albums else 0
                progress['unique_albums'] = max_albums
    
    @staticmethod
    def _update_genre_progress(progress: Dict, pin_data: Dict, criteria: Dict):
        """Update genre-related progress"""
        genre = pin_data.get('genre')
        city = pin_data.get('city')
        country = pin_data.get('country')
        created_at = pin_data.get('created_at')
        
        # Fix: Properly check for valid genre data (not None, empty, or "None" string)
        if genre and genre not in ['None', '', 'null', 'unknown']:
            # Track unique genres
            genres = set(progress.get('genres_played', []))
            genres.add(genre)
            progress['genres_played'] = list(genres)
            progress['unique_genres'] = len(genres)
            
            # Track pins by same genre
            genre_pins = progress.get('genre_pin_counts', {})
            genre_pins[genre] = genre_pins.get(genre, 0) + 1
            progress['genre_pin_counts'] = genre_pins
            
            # Update max pins for same genre
            progress['same_genre_pins'] = max(genre_pins.values()) if genre_pins else 0
            
            # NEW: Track unique genres per city for "Genre Explorer" challenge
            if city and 'unique_genres_in_city' in criteria:
                city_genres = progress.get('city_genres', {})
                if city not in city_genres:
                    city_genres[city] = []
                
                # Convert to set for operations, then back to list
                genre_set = set(city_genres[city])
                genre_set.add(genre)
                city_genres[city] = list(genre_set)
                
                progress['city_genres'] = city_genres
                
                # Update max unique genres in any single city
                max_genres_in_city = max(
                    len(genres) for genres in city_genres.values()
                ) if city_genres else 0
                progress['unique_genres_in_city'] = max_genres_in_city
            
            # NEW: Track same genre across different countries for "Genre Traveler"
            if country and 'same_genre_different_countries' in criteria:
                genre_countries = progress.get('genre_countries', {})
                if genre not in genre_countries:
                    genre_countries[genre] = []
                
                # Convert to set for operations, then back to list
                country_set = set(genre_countries[genre])
                country_set.add(country)
                genre_countries[genre] = list(country_set)
                
                progress['genre_countries'] = genre_countries
                
                # Update max countries for same genre
                max_countries_per_genre = max(
                    len(countries) for countries in genre_countries.values()
                ) if genre_countries else 0
                progress['same_genre_different_countries'] = max_countries_per_genre
            
            # NEW: Track different genres on same day for "Daily Genre Mix"
            if created_at and 'different_genres_same_day' in criteria:
                date_str = created_at.date().isoformat() if hasattr(created_at, 'date') else str(created_at)[:10]
                daily_genres = progress.get('daily_genres', {})
                if date_str not in daily_genres:
                    daily_genres[date_str] = []
                
                # Convert to set for operations, then back to list
                genre_set = set(daily_genres[date_str])
                genre_set.add(genre)
                daily_genres[date_str] = list(genre_set)
                
                progress['daily_genres'] = daily_genres
                
                # Count days with enough different genres
                required_genres = criteria.get('different_genres_same_day', 2)
                days_with_enough_genres = sum(
                    1 for genres in daily_genres.values() 
                    if len(genres) >= required_genres
                )
                progress['days_with_different_genres'] = days_with_enough_genres
            
            # NEW: Track weekend genres for "Weekend Warrior"
            if created_at and 'unique_genres_weekend' in criteria:
                if hasattr(created_at, 'weekday'):
                    is_weekend = created_at.weekday() >= 5  # Saturday=5, Sunday=6
                else:
                    # Fallback if created_at is a string
                    from datetime import datetime
                    try:
                        dt = datetime.fromisoformat(str(created_at).replace('Z', '+00:00'))
                        is_weekend = dt.weekday() >= 5
                    except:
                        is_weekend = False
                
                if is_weekend:
                    weekend_genres = set(progress.get('weekend_genres', []))
                    weekend_genres.add(genre)
                    progress['weekend_genres'] = list(weekend_genres)
                    progress['unique_genres_weekend'] = len(weekend_genres)
            
            # NEW: Check genre patterns for "Electronic Explorer" and similar challenges
            if 'genre_patterns' in criteria:
                patterns = criteria['genre_patterns']
                genre_lower = genre.lower()
                
                for pattern in patterns:
                    if pattern.lower() in genre_lower:
                        progress['genre_pattern_matched'] = True
                        progress['matched_pattern'] = pattern
                        break
            
            # Legacy: Special handling for "Genre Hopper" challenge (if it still exists)
            if 'artists_per_genre' in criteria:
                artist_id = pin_data.get('artist_id') or pin_data.get('artist_name')
                if artist_id:
                    genre_artists = progress.get('genre_artists', {})
                    if genre not in genre_artists:
                        genre_artists[genre] = []
                    
                    # Convert to set for operations, then back to list
                    artist_set = set(genre_artists[genre])
                    artist_set.add(artist_id)
                    genre_artists[genre] = list(artist_set)
                    
                    progress['genre_artists'] = genre_artists
                    
                    # Count genres with enough artists
                    required_artists = criteria['artists_per_genre']
                    genres_with_enough = sum(
                        1 for artists in genre_artists.values() 
                        if len(artists) >= required_artists
                    )
                    progress['genres_with_enough_artists'] = genres_with_enough
    
    @staticmethod
    def _update_time_progress(progress: Dict, pin_data: Dict, criteria: Dict):
        """Update time-related progress"""
        created_at = pin_data.get('created_at', timezone.now())
        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        
        # Track different days
        if 'different_days' in criteria:
            pin_dates = progress.get('pin_dates', [])
            date_str = created_at.date().isoformat()
            if date_str not in pin_dates:
                pin_dates.append(date_str)
                progress['pin_dates'] = pin_dates
                progress['different_days'] = len(pin_dates)
        
        # Check time range (e.g., midnight bop)
        if 'time_range' in criteria:
            time_range = criteria['time_range']
            start_hour = int(time_range['start'].split(':')[0])
            end_hour = int(time_range['end'].split(':')[0])
            
            hour = created_at.hour
            if start_hour <= hour < end_hour or (start_hour > end_hour and (hour >= start_hour or hour < end_hour)):
                progress['time_range_met'] = True
        
        # Track consecutive days (for streak challenges)
        if 'consecutive_days' in criteria:
            ProgressTracker._update_streak_progress(progress, created_at)
    
    @staticmethod
    def _update_streak_progress(progress: Dict, pin_date: datetime):
        """Update consecutive day streak progress"""
        today = pin_date.date()
        last_pin_date = progress.get('last_pin_date')
        
        if last_pin_date:
            last_date = datetime.fromisoformat(last_pin_date).date()
            days_diff = (today - last_date).days
            
            if days_diff == 1:
                # Consecutive day
                progress['current_streak'] = progress.get('current_streak', 0) + 1
            elif days_diff == 0:
                # Same day, no change to streak
                pass
            else:
                # Streak broken
                progress['current_streak'] = 1
        else:
            # First pin
            progress['current_streak'] = 1
        
        # Update max streak
        progress['max_streak'] = max(
            progress.get('max_streak', 0),
            progress.get('current_streak', 0)
        )
        
        progress['last_pin_date'] = today.isoformat()
    
    @staticmethod
    def _count_pins_in_radius(pin_locations: List[List[float]], radius_km: float) -> int:
        """Count pins within radius of any other pin"""
        if not pin_locations or len(pin_locations) < 2:
            return len(pin_locations)
        
        def haversine_distance(lat1, lon1, lat2, lon2):
            """Calculate distance between two coordinates in kilometers"""
            R = 6371  # Earth's radius in kilometers
            
            lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            
            a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))
            
            return R * c
        
        count = 0
        for i, (lat1, lon1) in enumerate(pin_locations):
            for j, (lat2, lon2) in enumerate(pin_locations):
                if i != j:
                    distance = haversine_distance(lat1, lon1, lat2, lon2)
                    if distance <= radius_km:
                        count += 1
                        break  # Only count each pin once
        
        return count
    
    @staticmethod
    def _calculate_max_distance(pin_locations: List[List[float]]) -> float:
        """Calculate maximum distance between any two pins"""
        if not pin_locations or len(pin_locations) < 2:
            return 0.0
        
        def haversine_distance(lat1, lon1, lat2, lon2):
            """Calculate distance between two coordinates in kilometers"""
            R = 6371  # Earth's radius in kilometers
            
            lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            
            a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))
            
            return R * c
        
        max_distance = 0.0
        for i, (lat1, lon1) in enumerate(pin_locations):
            for j, (lat2, lon2) in enumerate(pin_locations):
                if i != j:
                    distance = haversine_distance(lat1, lon1, lat2, lon2)
                    max_distance = max(max_distance, distance)
        
        return max_distance
    
    @staticmethod
    def update_social_progress(user, action_type: str, data: Dict[str, Any]):
        """
        Update progress for social achievements
        
        Args:
            user: User instance
            action_type: Type of social action ('reaction_given', 'reaction_received', 'share', etc.)
            data: Action-specific data
        """
        achievements = Achievement.objects.filter(type='social')
        
        for achievement in achievements:
            user_achievement, created = UserAchievement.objects.get_or_create(
                user=user,
                achievement=achievement,
                defaults={'progress': {}}
            )
            
            ProgressTracker._update_social_criteria(user_achievement, action_type, data)
    
    @staticmethod
    def _update_social_criteria(user_achievement: UserAchievement, action_type: str, data: Dict):
        """Update social-related progress"""
        progress = user_achievement.progress or {}
        criteria = user_achievement.achievement.criteria
        
        # === REACTIONS GIVEN (from votes and likes) ===
        if action_type == 'reaction_given':
            progress['reactions_given'] = progress.get('reactions_given', 0) + 1
            
            # Update daily reaction streak
            today = timezone.now().date().isoformat()
            daily_reactions = progress.get('daily_reactions', {})
            daily_reactions[today] = daily_reactions.get(today, 0) + 1
            progress['daily_reactions'] = daily_reactions
            
            # Update consecutive days streak
            ProgressTracker._update_reaction_streak(progress, timezone.now().date())
            
        # === REACTIONS RECEIVED (from votes and likes) ===
        elif action_type == 'reaction_received':
            progress['reactions_received'] = progress.get('reactions_received', 0) + 1
            
            # Track unique reactors
            reactor_id = data.get('reactor_user_id')
            if reactor_id:
                unique_reactors = set(progress.get('unique_reactor_ids', []))
                unique_reactors.add(str(reactor_id))
                progress['unique_reactor_ids'] = list(unique_reactors)
                progress['unique_reactors'] = len(unique_reactors)
            
            # Check for legend rank reaction
            if data.get('reactor_rank') == 'legend':
                progress['legend_rank_reaction_received'] = True
            
            # Track per-pin reaction counts for multi-hit challenges
            pin_id = data.get('pin_id')
            if pin_id:
                pin_reactions = progress.get('pin_reactions', {})
                pin_id_str = str(pin_id)
                if pin_id_str not in pin_reactions:
                    pin_reactions[pin_id_str] = {'reactions': 0, 'replays': 0}
                pin_reactions[pin_id_str]['reactions'] += 1
                progress['pin_reactions'] = pin_reactions
                
                # Check for pins with enough reactions for multi-hit challenges
                if 'reactions_per_pin' in criteria:
                    required_reactions = criteria['reactions_per_pin']
                    pins_with_enough = sum(
                        1 for pin_data in pin_reactions.values()
                        if pin_data['reactions'] >= required_reactions
                    )
                    progress['pins_with_enough_reactions'] = pins_with_enough
        
        # === SHARES ===
        elif action_type == 'share':
            pin_id = data.get('pin_id')
            if pin_id:
                shared_pins = set(progress.get('shared_pin_ids', []))
                shared_pins.add(str(pin_id))
                progress['shared_pin_ids'] = list(shared_pins)
                progress['unique_shares'] = len(shared_pins)
        
        # === COMMENTS ===
        elif action_type == 'comment_given':
            progress['comments_given'] = progress.get('comments_given', 0) + 1
            
            # Track unique pins commented on
            pin_id = data.get('pin_id')
            if pin_id:
                commented_pins = set(progress.get('commented_pin_ids', []))
                commented_pins.add(str(pin_id))
                progress['commented_pin_ids'] = list(commented_pins)
                progress['unique_pins_commented'] = len(commented_pins)
        
        elif action_type == 'comment_received':
            progress['comments_received'] = progress.get('comments_received', 0) + 1
            
            # Track unique commenters
            commenter_id = data.get('commenter_user_id')
            if commenter_id:
                unique_commenters = set(progress.get('unique_commenter_ids', []))
                unique_commenters.add(str(commenter_id))
                progress['unique_commenter_ids'] = list(unique_commenters)
                progress['unique_commenters'] = len(unique_commenters)
        
        # === WEEKLY CHALLENGES ===
        elif action_type == 'weekly_challenge_completed':
            challenge_id = data.get('challenge_id')
            if challenge_id:
                completed_challenges = set(progress.get('completed_weekly_challenges', []))
                completed_challenges.add(str(challenge_id))
                progress['completed_weekly_challenges'] = list(completed_challenges)
                progress['weekly_challenges_completed'] = len(completed_challenges)
        
        # === REPLAYS ===
        elif action_type == 'replay':
            progress['total_replays'] = progress.get('total_replays', 0) + 1
            
            # Track per-pin replays for viral challenges
            pin_id = data.get('pin_id')
            if pin_id:
                pin_reactions = progress.get('pin_reactions', {})
                pin_id_str = str(pin_id)
                if pin_id_str not in pin_reactions:
                    pin_reactions[pin_id_str] = {'reactions': 0, 'replays': 0}
                pin_reactions[pin_id_str]['replays'] += 1
                progress['pin_reactions'] = pin_reactions
                
                # Check for viral pins (high reactions + replays)
                if 'single_pin_reactions' in criteria and 'single_pin_replays' in criteria:
                    required_reactions = criteria['single_pin_reactions']
                    required_replays = criteria['single_pin_replays']
                    
                    for pin_data in pin_reactions.values():
                        if (pin_data['reactions'] >= required_reactions and 
                            pin_data['replays'] >= required_replays):
                            progress['viral_pin_achieved'] = True
                            break
        
        # === REFERRALS ===
        elif action_type == 'referral':
            referral_count = data.get('referral_count', 1)
            progress['successful_referrals'] = progress.get('successful_referrals', 0) + referral_count
        
        # === REMOVAL ACTIONS ===
        elif action_type == 'reaction_removed':
            # Subtract from reactions given
            if progress.get('reactions_given', 0) > 0:
                progress['reactions_given'] -= 1
                
        elif action_type == 'reaction_lost':
            # Subtract from reactions received
            if progress.get('reactions_received', 0) > 0:
                progress['reactions_received'] -= 1
                
            # Remove from unique reactors if needed
            reactor_id = data.get('reactor_user_id')
            if reactor_id:
                unique_reactors = set(progress.get('unique_reactor_ids', []))
                unique_reactors.discard(str(reactor_id))
                progress['unique_reactor_ids'] = list(unique_reactors)
                progress['unique_reactors'] = len(unique_reactors)
                
            # Update per-pin reaction counts
            pin_id = data.get('pin_id')
            if pin_id:
                pin_reactions = progress.get('pin_reactions', {})
                pin_id_str = str(pin_id)
                if pin_id_str in pin_reactions and pin_reactions[pin_id_str]['reactions'] > 0:
                    pin_reactions[pin_id_str]['reactions'] -= 1
                    progress['pin_reactions'] = pin_reactions
        
        elif action_type == 'share_removed':
            # Remove from shared pins
            pin_id = data.get('pin_id')
            if pin_id:
                shared_pins = set(progress.get('shared_pin_ids', []))
                shared_pins.discard(str(pin_id))
                progress['shared_pin_ids'] = list(shared_pins)
                progress['unique_shares'] = len(shared_pins)
        
        elif action_type == 'comment_removed':
            # Subtract from comments given
            if progress.get('comments_given', 0) > 0:
                progress['comments_given'] -= 1
            
            # Remove from commented pins if needed
            pin_id = data.get('pin_id')
            if pin_id:
                commented_pins = set(progress.get('commented_pin_ids', []))
                commented_pins.discard(str(pin_id))
                progress['commented_pin_ids'] = list(commented_pins)
                progress['unique_pins_commented'] = len(commented_pins)
        
        elif action_type == 'comment_lost':
            # Subtract from comments received
            if progress.get('comments_received', 0) > 0:
                progress['comments_received'] -= 1
            
            # Remove from unique commenters if needed
            commenter_id = data.get('commenter_user_id')
            if commenter_id:
                unique_commenters = set(progress.get('unique_commenter_ids', []))
                unique_commenters.discard(str(commenter_id))
                progress['unique_commenter_ids'] = list(unique_commenters)
                progress['unique_commenters'] = len(unique_commenters)
        
        # Save updated progress
        user_achievement.progress = progress
        user_achievement.save()
        user_achievement.check_completion()
    
    @staticmethod
    def _update_reaction_streak(progress: Dict, reaction_date):
        """Update consecutive day reaction streak"""
        today = reaction_date
        last_reaction_date = progress.get('last_reaction_date')
        
        if last_reaction_date:
            last_date = datetime.fromisoformat(last_reaction_date).date()
            days_diff = (today - last_date).days
            
            if days_diff == 1:
                # Consecutive day
                progress['current_streak'] = progress.get('current_streak', 0) + 1
            elif days_diff == 0:
                # Same day, no change to streak
                pass
            else:
                # Streak broken
                progress['current_streak'] = 1
        else:
            # First reaction
            progress['current_streak'] = 1
        
        # Update max streak
        progress['max_streak'] = max(
            progress.get('max_streak', 0),
            progress.get('current_streak', 0)
        )
        
        progress['last_reaction_date'] = today.isoformat()
    
    @staticmethod
    def update_special_progress(user, event_type: str, data: Dict[str, Any]):
        """
        Update progress for special achievements (API-driven, early adopter, etc.)
        
        Args:
            user: User instance  
            event_type: Type of special event
            data: Event-specific data
        """
        achievements = Achievement.objects.all()
        
        for achievement in achievements:
            criteria = achievement.criteria
            
            if event_type == 'chart_verification' and 'chart_topping_artists' in criteria:
                user_achievement, created = UserAchievement.objects.get_or_create(
                    user=user,
                    achievement=achievement,
                    defaults={'progress': {}}
                )
                
                progress = user_achievement.progress or {}
                progress['chart_topping_artists'] = progress.get('chart_topping_artists', 0) + 1
                progress['api_verified'] = True
                
                user_achievement.progress = progress
                user_achievement.save()
                user_achievement.check_completion()
            
            elif event_type == 'early_release' and 'days_since_release' in criteria:
                days_since = data.get('days_since_release', 0)
                required_days = criteria['days_since_release']
                
                if days_since <= required_days:
                    user_achievement, created = UserAchievement.objects.get_or_create(
                        user=user,
                        achievement=achievement,
                        defaults={'progress': {}}
                    )
                    
                    progress = user_achievement.progress or {}
                    progress['early_adopter_verified'] = True
                    
                    user_achievement.progress = progress
                    user_achievement.save()
                    user_achievement.check_completion() 
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from gamification.models import UserAchievement
from gamification.services.progress_tracker import ProgressTracker
from pins.models import Pin
from pins.services import GeocodingService

User = get_user_model()


class Command(BaseCommand):
    help = 'Fix corrupted genre and artist challenge progress by reprocessing existing pins'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='Username to fix (optional, fixes all users if not specified)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without actually making changes',
        )

    def handle(self, *args, **options):
        username = options.get('username')
        dry_run = options.get('dry_run')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get users to process
        if username:
            users = User.objects.filter(username=username)
            if not users.exists():
                self.stdout.write(self.style.ERROR(f'User "{username}" not found'))
                return
        else:
            users = User.objects.all()
        
        self.stdout.write(f'Processing {users.count()} user(s)...')
        
        for user in users:
            self.stdout.write(f'\n🔄 Processing user: {user.username}')
            self.fix_user_music_challenges(user, dry_run)
    
    def fix_user_music_challenges(self, user, dry_run=False):
        """Fix genre and artist challenges for a specific user"""
        
        # Get all pins by this user
        user_pins = Pin.objects.filter(owner=user).order_by('created_at')
        
        if not user_pins.exists():
            self.stdout.write(f'  ℹ️  No pins found for {user.username}')
            return
        
        self.stdout.write(f'  📍 Found {user_pins.count()} pins')
        
        # Reset genre and artist challenge progress
        genre_and_artist_achievements = UserAchievement.objects.filter(
            user=user,
            achievement__type__in=['genre', 'artist']
        )
        
        self.stdout.write(f'  🎯 Resetting {genre_and_artist_achievements.count()} genre/artist challenges')
        
        if not dry_run:
            # Clear corrupted progress for genre/artist challenges
            for ua in genre_and_artist_achievements:
                # Keep non-music progress, clear music-specific fields
                progress = ua.progress or {}
                music_fields = [
                    'genres_played', 'unique_genres', 'genre_pin_counts', 'same_genre_pins',
                    'artists_played', 'unique_artists', 'artist_pin_counts', 'same_artist_pins',
                    'city_genres', 'unique_genres_in_city', 'city_artists', 'unique_artists_in_city',
                    'artist_albums', 'unique_albums'
                ]
                for field in music_fields:
                    progress.pop(field, None)
                
                ua.progress = progress
                ua.completed_at = None  # Allow recompletion
                ua.save()
        
        # Reprocess each pin with fixed logic
        valid_genres = []
        valid_artists = []
        
        for pin in user_pins:
            # Check if pin has valid music data
            has_valid_genre = pin.genre and pin.genre not in ['None', '', 'null', 'unknown']
            has_valid_artist = pin.track_artist and pin.track_artist not in ['None', '', 'null']
            
            if has_valid_genre:
                valid_genres.append(pin.genre)
            if has_valid_artist:
                valid_artists.append(pin.track_artist)
            
            if not dry_run and (has_valid_genre or has_valid_artist):
                # Get geographic data for the pin
                latitude = pin.location.y
                longitude = pin.location.x
                geo_data = GeocodingService.reverse_geocode(
                    latitude, longitude, location_name=pin.location_name
                )
                
                # Create pin data like the signal would
                pin_data = {
                    'current_pin_id': pin.id,
                    'latitude': latitude,
                    'longitude': longitude,
                    'city': geo_data.get('city'),
                    'state': geo_data.get('state'),
                    'country': geo_data.get('country'),
                    'continent': geo_data.get('continent'),
                    'district': geo_data.get('district'),
                    'location_name': pin.location_name,
                    'artist_name': pin.track_artist,
                    'album_name': getattr(pin, 'album', None),
                    'genre': pin.genre,
                    'service': getattr(pin, 'service', None),
                    'created_at': pin.created_at,
                    'time_of_day': pin.created_at.hour if pin.created_at else None,
                    'is_public': not pin.is_private
                }
                
                # Update progress using fixed logic
                ProgressTracker.update_pin_progress(user, pin_data)
        
        # Report results
        unique_valid_genres = len(set(valid_genres))
        unique_valid_artists = len(set(valid_artists))
        
        self.stdout.write(f'  🎵 Valid genres found: {unique_valid_genres} unique ({valid_genres[:5]}{"..." if len(valid_genres) > 5 else ""})')
        self.stdout.write(f'  🎤 Valid artists found: {unique_valid_artists} unique ({valid_artists[:3]}{"..." if len(valid_artists) > 3 else ""})')
        
        if not dry_run:
            # Check final progress
            final_achievements = UserAchievement.objects.filter(
                user=user,
                achievement__type__in=['genre', 'artist'],
                completed_at__isnull=False
            )
            self.stdout.write(f'  ✅ Completed challenges after fix: {final_achievements.count()}')
            
            for ua in final_achievements:
                self.stdout.write(f'    🏆 {ua.achievement.name}')
        
        self.stdout.write(f'  ✅ Finished processing {user.username}') 
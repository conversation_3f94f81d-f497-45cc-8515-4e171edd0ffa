from django.core.management.base import BaseCommand
from django.db import transaction
from gamification.models import UserAchievement
from gamification.services.progress_tracker import ProgressTracker
from pins.models import Pin
from pins.services import GeocodingService
from users.models import User
from django.core.cache import cache


class Command(BaseCommand):
    help = 'Fix corrupted location challenge progress by reprocessing all pins with correct geocoding'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=str,
            help='Username to fix challenges for (if not specified, fixes all users)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without making changes',
        )

    def handle(self, *args, **options):
        username = options.get('user')
        dry_run = options.get('dry_run', False)
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get users to process
        if username:
            try:
                users = [User.objects.get(username=username)]
                self.stdout.write(f"Processing user: {username}")
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'User "{username}" not found'))
                return
        else:
            users = User.objects.all()
            self.stdout.write(f"Processing all {users.count()} users")

        total_fixes = 0
        
        for user in users:
            user_fixes = self.fix_user_location_challenges(user, dry_run)
            total_fixes += user_fixes
            
            if user_fixes > 0:
                self.stdout.write(
                    self.style.SUCCESS(f'Fixed {user_fixes} location challenges for {user.username}')
                )

        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'DRY RUN: Would fix {total_fixes} location challenges total')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully fixed {total_fixes} location challenges total')
            )

    def fix_user_location_challenges(self, user, dry_run=False):
        """Fix location challenges for a specific user"""
        
        # Get all location-type achievements that the user has progress on
        location_achievements = UserAchievement.objects.filter(
            user=user,
            achievement__type='location'
        ).select_related('achievement')
        
        if not location_achievements.exists():
            return 0
            
        self.stdout.write(f"  Processing {location_achievements.count()} location challenges for {user.username}")
        
        # Show current corrupted data
        self.stdout.write("  Current corrupted data:")
        for ua in location_achievements[:3]:  # Show first 3 as examples
            progress = ua.progress or {}
            countries = progress.get('countries_visited', [])
            cities = progress.get('cities_visited', [])
            if countries or cities:
                self.stdout.write(f"    {ua.achievement.name}:")
                if countries:
                    self.stdout.write(f"      Countries: {countries}")
                if cities:
                    self.stdout.write(f"      Cities: {cities}")
        
        if dry_run:
            return location_achievements.count()
        
        # Get all pins for this user to reprocess
        user_pins = Pin.objects.filter(owner=user).order_by('created_at')
        
        if not user_pins.exists():
            self.stdout.write(f"    No pins found for {user.username}")
            return 0
            
        self.stdout.write(f"    Reprocessing {user_pins.count()} pins...")
        
        with transaction.atomic():
            # Reset all location challenge progress
            for ua in location_achievements:
                # Keep only non-location progress data
                old_progress = ua.progress or {}
                new_progress = {}
                
                # Keep non-location fields (like total_pins)
                location_fields = [
                    'cities_visited', 'different_cities', 'states_visited', 'different_states',
                    'countries_visited', 'different_countries', 'visited_continents', 
                    'different_continents', 'districts_visited', 'different_districts',
                    'pin_locations', 'pins_in_radius', 'max_distance_km', 'small_town_pins',
                    'big_city_pins'
                ]
                
                for key, value in old_progress.items():
                    if key not in location_fields:
                        new_progress[key] = value
                
                ua.progress = new_progress
                ua.completed_at = None  # Reset completion
                ua.save()
            
            # Clear geocoding cache for this user's pins
            for pin in user_pins:
                if pin.location:
                    lat = pin.location.y
                    lng = pin.location.x
                    cache_key = f"geocode_{round(lat, 3)}_{round(lng, 3)}"
                    cache.delete(cache_key)
            
            # Reprocess all pins with fixed geocoding
            for pin in user_pins:
                if pin.location:
                    # Get fixed geographic data
                    latitude = pin.location.y
                    longitude = pin.location.x
                    
                    geo_data = GeocodingService.reverse_geocode(
                        latitude, longitude, location_name=pin.location_name
                    )
                    
                    # Create comprehensive pin data for progress tracking
                    pin_data = {
                        'current_pin_id': pin.id,
                        'latitude': latitude,
                        'longitude': longitude,
                        'city': geo_data.get('city'),
                        'state': geo_data.get('state'),
                        'country': geo_data.get('country'),
                        'continent': geo_data.get('continent'),
                        'district': geo_data.get('district'),
                        'population': geo_data.get('population'),
                        'location_name': pin.location_name,
                        'artist_id': None,
                        'artist_name': pin.track_artist,
                        'album_id': None,
                        'album_name': pin.album,
                        'genre': pin.genre,
                        'service': pin.service,
                        'created_at': pin.created_at,
                        'time_of_day': pin.created_at.hour if pin.created_at else None,
                        'is_public': True  # Assume public for challenges
                    }
                    
                    # Update progress with fixed data (only location challenges will be affected)
                    ProgressTracker.update_pin_progress(user, pin_data)
        
        # Count how many challenges were completed after fixing
        completed_count = location_achievements.filter(completed_at__isnull=False).count()
        
        self.stdout.write(f"    ✅ Reprocessed {user_pins.count()} pins")
        self.stdout.write(f"    ✅ {completed_count} location challenges completed after fixing")
        
        return location_achievements.count() 
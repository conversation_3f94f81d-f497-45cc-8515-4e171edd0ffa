from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q, Count, Sum
from django.utils import timezone
from django.core.cache import cache
from asgiref.sync import async_to_sync
from ..models import Achievement, UserAchievement
from ..services.xp_calculator import XPCalculator
from ..services.progress_tracker import ProgressTracker
from ..consumers import AchievementConsumer
from .serializers import (
    AchievementSerializer,
    UserAchievementSerializer,
    UserAchievementListSerializer
)

class AchievementViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing achievements and challenges
    """
    queryset = Achievement.objects.all()
    serializer_class = AchievementSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset based on type and completion status"""
        queryset = super().get_queryset()
        
        # Filter by type if specified
        achievement_type = self.request.query_params.get('type')
        if achievement_type:
            queryset = queryset.filter(type=achievement_type)
        
        # Filter by tier if specified
        tier = self.request.query_params.get('tier')
        if tier:
            queryset = queryset.filter(tier=tier)
        
        # Handle secret achievements/challenges
        if not self.request.query_params.get('include_secret'):
            # Only show secret ones if completed
            completed_ids = UserAchievement.objects.filter(
                user=self.request.user,
                completed_at__isnull=False
            ).values_list('achievement_id', flat=True)
            
            queryset = queryset.filter(
                Q(is_secret=False) | Q(id__in=completed_ids)
            )
        
        return queryset
    
    @action(detail=False)
    def completed(self, request):
        """Get completed achievements for the user"""
        user_achievements = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=False
        ).select_related('achievement')
        
        achievements = [ua.achievement for ua in user_achievements]
        serializer = self.get_serializer(achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False)
    def in_progress(self, request):
        """Get in-progress achievements for the user"""
        user_achievements = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=True
        ).select_related('achievement')
        
        achievements = [ua.achievement for ua in user_achievements]
        serializer = self.get_serializer(achievements, many=True)
        return Response(serializer.data)

    @action(detail=False)
    def featured(self, request):
        """Get featured achievements/challenges for display"""
        # Get highest tier achievements from each category
        featured = []
        for type_code, type_name in Achievement.TYPES:
            achievement = self.get_queryset().filter(
                type=type_code
            ).order_by('-tier', '-xp_reward').first()
            
            if achievement:
                serializer = self.get_serializer(achievement)
                featured.append(serializer.data)
        
        return Response(featured)

    @action(detail=False)
    def by_category(self, request):
        """Get achievements grouped by category"""
        categories = {}
        for type_code, type_name in Achievement.TYPES:
            achievements = self.get_queryset().filter(type=type_code)
            if achievements.exists():
                serializer = self.get_serializer(achievements, many=True)
                categories[type_code] = {
                    'name': type_name,
                    'achievements': serializer.data
                }
        
        return Response(categories)
    
    @action(detail=False)
    def category(self, request, category_type=None):
        """Get achievements for a specific category"""
        if not category_type:
            return Response(
                {'error': 'Category type is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate category type
        valid_types = dict(Achievement.TYPES)
        if category_type not in valid_types:
            return Response(
                {'error': f'Invalid category type. Valid types: {list(valid_types.keys())}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get achievements for this category
        achievements = self.get_queryset().filter(type=category_type)
        serializer = self.get_serializer(achievements, many=True)
        
        return Response({
            'category': category_type,
            'name': valid_types[category_type],
            'achievements': serializer.data
        })

    @action(detail=False, methods=['post'])
    def check_completions(self, request):
        """
        Check for achievement completions after an action
        Returns immediate feedback about completions and progress
        """
        action_type = request.data.get('action')
        action_data = request.data.get('data', {})
        
        if not action_type:
            return Response(
                {'error': 'action field is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Track progress based on action
        if action_type == 'pin_created':
            ProgressTracker.update_pin_progress(request.user, action_data)
        elif action_type in ['reaction_given', 'reaction_received', 'comment_given', 'comment_received']:
            ProgressTracker.update_social_progress(request.user, action_type, action_data)

        # Get newly completed achievements
        completed_achievements = UserAchievement.objects.filter(
            user=request.user,
            completed_at__gte=timezone.now() - timezone.timedelta(seconds=5)
        ).select_related('achievement')

        # Calculate XP gained
        xp_gained = sum(ua.achievement.xp_reward for ua in completed_achievements)
        
        # Get level change info if XP was gained
        level_changed = False
        new_level = None
        if xp_gained > 0:
            level_info = XPCalculator.update_user_xp_and_level(request.user)
            level_changed = level_info['level_changed']
            if level_changed:
                new_level = {
                    'level': level_info['new_level'],
                    'badge_name': level_info['badge_name']
                }

        # Get progress updates for near-completion achievements
        progress_updates = []
        near_complete = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=True
        ).select_related('achievement')

        for ua in near_complete:
            if ua.progress and 'current_count' in ua.progress:
                current = ua.progress['current_count']
                required = ua.achievement.criteria.get('required_count', 0)
                if required > 0:
                    percentage = (current / required) * 100
                    if percentage >= 90:  # Only show achievements that are close
                        progress_updates.append({
                            'achievement': ua.achievement.name,
                            'progress': f"{current}/{required}",
                            'percentage': round(percentage, 1)
                        })

        response_data = {
            'achievements_completed': [{
                'name': ua.achievement.name,
                'xp_earned': ua.achievement.xp_reward,
                'icon': ua.achievement.icon_data,
                'description': ua.achievement.description
            } for ua in completed_achievements],
            'xp_gained': xp_gained,
            'level_changed': level_changed,
            'new_level': new_level,
            'progress_updates': progress_updates
        }

        # Send WebSocket update if there were completions
        if completed_achievements or progress_updates:
            async_to_sync(AchievementConsumer.notify_achievement)(
                request.user.id,
                {
                    'type': 'achievement_update',
                    'data': response_data
                }
            )

        return Response(response_data)

    @action(detail=False, methods=['get'])
    def live_progress(self, request):
        """
        Get achievements that are close to completion (90%+ progress)
        """
        near_complete = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=True
        ).select_related('achievement')

        progress_data = []
        for ua in near_complete:
            if ua.progress and 'current_count' in ua.progress:
                current = ua.progress['current_count']
                required = ua.achievement.criteria.get('required_count', 0)
                if required > 0:
                    percentage = (current / required) * 100
                    if percentage >= 90:
                        progress_data.append({
                            'achievement': ua.achievement.name,
                            'description': ua.achievement.description,
                            'icon': ua.achievement.icon_data,
                            'progress': f"{current}/{required}",
                            'percentage': round(percentage, 1),
                            'xp_reward': ua.achievement.xp_reward
                        })

        return Response({
            'near_complete_achievements': progress_data
        })

    @action(detail=False, methods=['get'])
    def rank_badge_data(self, request):
        """
        SUPER FAST endpoint specifically for User Rank Badge Flutter widget
        Returns ALL data needed in a single optimized call with caching
        """
        cache_key = f"rank_badge_data:{request.user.id}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return Response(cached_data)
        
        # Get user level info using centralized XP calculator
        level_info = XPCalculator.get_user_level_info(request.user)
        
        # Get all rank levels for the modal
        all_ranks = [
            {
                'id': f'level_{level}',
                'name': data['name'],
                'level': level,
                'required_xp': data['required_xp'],
                'emoji': self._get_rank_emoji(data['name']),
                'primary_color': self._get_rank_colors(data['name'])['primary'],
                'background_color': self._get_rank_colors(data['name'])['background'],
                'unlocked': level_info['total_xp'] >= data['required_xp']
            }
            for level, data in enumerate([
                {'name': 'Basement Bopper', 'required_xp': 0},
                {'name': 'Selector', 'required_xp': 500},
                {'name': 'Tastemaker', 'required_xp': 1500},
                {'name': 'Trendsetter', 'required_xp': 3500},
                {'name': 'Icon', 'required_xp': 7000},
                {'name': 'Architect', 'required_xp': 12000},
                {'name': 'Legend', 'required_xp': 20000},
            ], 1)
        ]
        
        # Get recent achievements (last 5)
        recent_achievements = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=False
        ).select_related('achievement').order_by('-completed_at')[:5]
        
        recent_data = [{
            'name': ua.achievement.name,
            'xp_earned': ua.achievement.xp_reward,
            'completed_at': ua.completed_at,
            'icon': ua.achievement.icon_data
        } for ua in recent_achievements]
        
        # Get close-to-completion achievements
        near_complete = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=True
        ).select_related('achievement')
        
        close_achievements = []
        for ua in near_complete:
            if ua.progress and 'current_count' in ua.progress:
                current = ua.progress['current_count']
                required = ua.achievement.criteria.get('required_count', 0)
                if required > 0:
                    percentage = (current / required) * 100
                    if percentage >= 75:  # Show achievements at 75%+ for badge widget
                        close_achievements.append({
                            'name': ua.achievement.name,
                            'progress': current,
                            'required': required,
                            'percentage': round(percentage, 1),
                            'xp_reward': ua.achievement.xp_reward
                        })
        
        # Build response data
        response_data = {
            # Current rank info
            'current_rank': {
                'id': f"level_{level_info['current_level']['level']}",
                'name': level_info['current_level']['name'],
                'level': level_info['current_level']['level'],
                'emoji': self._get_rank_emoji(level_info['current_level']['name']),
                'primary_color': self._get_rank_colors(level_info['current_level']['name'])['primary'],
                'background_color': self._get_rank_colors(level_info['current_level']['name'])['background'],
            },
            
            # Next rank info (if exists)
            'next_rank': {
                'id': f"level_{level_info['next_level']['level']}",
                'name': level_info['next_level']['name'],
                'level': level_info['next_level']['level'],
                'emoji': self._get_rank_emoji(level_info['next_level']['name']),
                'primary_color': self._get_rank_colors(level_info['next_level']['name'])['primary'],
                'background_color': self._get_rank_colors(level_info['next_level']['name'])['background'],
            } if level_info['next_level'] else None,
            
            # XP and progress info
            'total_xp': level_info['total_xp'],
            'current_xp': level_info['progress_xp'],
            'next_level_xp': level_info['required_xp'],
            'xp_progress': round(level_info['progress_percentage'], 1),
            'user_level': level_info['current_level']['level'],
            
            # All ranks for modal
            'all_ranks': all_ranks,
            
            # Recent activity
            'recent_achievements': recent_data,
            'close_achievements': close_achievements,
            
            # Stats
            'stats': {
                'total_achievements': UserAchievement.objects.filter(user=request.user, completed_at__isnull=False).count(),
                'total_possible': Achievement.objects.count(),
                'completion_rate': round((UserAchievement.objects.filter(user=request.user, completed_at__isnull=False).count() / Achievement.objects.count()) * 100, 1) if Achievement.objects.count() > 0 else 0
            }
        }
        
        # Cache for 2 minutes (fast updates but not too frequent)
        cache.set(cache_key, response_data, timeout=120)
        
        return Response(response_data)
    
    def _get_rank_emoji(self, rank_name):
        """Get emoji for rank"""
        emojis = {
            'Basement Bopper': '🎧',
            'Selector': '🎵',
            'Tastemaker': '🎶',
            'Trendsetter': '🎤',
            'Icon': '⭐',
            'Architect': '🏗️',
            'Legend': '👑'
        }
        return emojis.get(rank_name, '🎧')
    
    def _get_rank_colors(self, rank_name):
        """Get colors for rank"""
        colors = {
            'Basement Bopper': {'primary': '#1A1A1A', 'background': '#2D2D2D'},
            'Selector': {'primary': '#4A90E2', 'background': '#2171C7'},
            'Tastemaker': {'primary': '#9B51E0', 'background': '#6B2E9E'},
            'Trendsetter': {'primary': '#F2994A', 'background': '#D97B29'},
            'Icon': {'primary': '#EB5757', 'background': '#C62828'},
            'Architect': {'primary': '#27AE60', 'background': '#1E8449'},
            'Legend': {'primary': '#F2C94C', 'background': '#DBA520'}
        }
        return colors.get(rank_name, colors['Basement Bopper'])

    @action(detail=False, methods=['get'])
    def quick_progress(self, request):
        """
        ULTRA FAST endpoint for real-time progress updates
        Returns only essential data for progress bar updates
        """
        # Get user level info using centralized XP calculator
        level_info = XPCalculator.get_user_level_info(request.user)
        
        return Response({
            'total_xp': level_info['total_xp'],
            'current_xp': level_info['progress_xp'],
            'next_level_xp': level_info['required_xp'],
            'xp_progress': round(level_info['progress_percentage'], 1),
            'user_level': level_info['current_level']['level'],
            'current_rank_name': level_info['current_level']['name'],
            'next_rank_name': level_info['next_level']['name'] if level_info['next_level'] else None,
            'level_changed': False,  # Will be true when called after action
            'timestamp': timezone.now().isoformat()
        })

    @action(detail=False, methods=['post'])
    def action_response(self, request):
        """
        IMMEDIATE response endpoint for actions (pin creation, reactions, etc.)
        Returns XP gained, level changes, and achievement completions in real-time
        """
        action_type = request.data.get('action')
        action_data = request.data.get('data', {})
        
        if not action_type:
            return Response(
                {'error': 'action field is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get XP before action
        xp_before = XPCalculator.get_user_total_xp(request.user)
        level_before = XPCalculator.get_user_level_info(request.user)

        # Track progress based on action
        if action_type == 'pin_created':
            ProgressTracker.update_pin_progress(request.user, action_data)
        elif action_type in ['reaction_given', 'reaction_received', 'comment_given', 'comment_received']:
            ProgressTracker.update_social_progress(request.user, action_type, action_data)

        # Get XP after action
        xp_after = XPCalculator.get_user_total_xp(request.user)
        level_after = XPCalculator.get_user_level_info(request.user)
        
        # Calculate changes
        xp_gained = xp_after - xp_before
        level_changed = level_after['current_level']['level'] != level_before['current_level']['level']
        
        # Get newly completed achievements (last 10 seconds)
        completed_achievements = UserAchievement.objects.filter(
            user=request.user,
            completed_at__gte=timezone.now() - timezone.timedelta(seconds=10)
        ).select_related('achievement')

        response_data = {
            # XP and level changes
            'xp_gained': xp_gained,
            'level_changed': level_changed,
            'new_level': level_after['current_level'] if level_changed else None,
            
            # Updated progress
            'total_xp': level_after['total_xp'],
            'current_xp': level_after['progress_xp'],
            'next_level_xp': level_after['required_xp'],
            'xp_progress': round(level_after['progress_percentage'], 1),
            'user_level': level_after['current_level']['level'],
            'current_rank_name': level_after['current_level']['name'],
            
            # Achievement completions
            'achievements_completed': [{
                'name': ua.achievement.name,
                'xp_earned': ua.achievement.xp_reward,
                'icon': ua.achievement.icon_data,
                'description': ua.achievement.description
            } for ua in completed_achievements],
            
            # Action feedback
            'action_successful': True,
            'message': f"Action '{action_type}' processed successfully",
            'timestamp': timezone.now().isoformat()
        }

        # Send WebSocket update if there were changes
        if xp_gained > 0 or completed_achievements:
            # Clear cache for rank badge data
            cache_key = f"rank_badge_data:{request.user.id}"
            cache.delete(cache_key)
            
            # Send WebSocket notification
            async_to_sync(AchievementConsumer.notify_achievement)(
                request.user.id,
                {
                    'type': 'progress_update',
                    'data': response_data
                }
            )

        return Response(response_data)

class UserAchievementViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing user's achievements and challenges
    """
    serializer_class = UserAchievementSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get user's achievements with optional filtering"""
        queryset = UserAchievement.objects.filter(user=self.request.user)
        
        # Filter by completion status
        status = self.request.query_params.get('status')
        if status == 'completed':
            queryset = queryset.filter(completed_at__isnull=False)
        elif status == 'in_progress':
            queryset = queryset.filter(completed_at__isnull=True)
        
        # Filter by type
        achievement_type = self.request.query_params.get('type')
        if achievement_type:
            queryset = queryset.filter(achievement__type=achievement_type)
        
        # Filter by tier
        tier = self.request.query_params.get('tier')
        if tier:
            queryset = queryset.filter(achievement__tier=tier)
        
        return queryset.select_related('achievement')
    
    def get_serializer_class(self):
        """Use list serializer for list view"""
        if self.action == 'list':
            return UserAchievementListSerializer
        return UserAchievementSerializer
    
    @action(detail=False, methods=['get'])
    def completed(self, request):
        """Get completed user achievements"""
        completed_achievements = self.get_queryset().filter(completed_at__isnull=False)
        serializer = UserAchievementListSerializer(completed_achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def in_progress(self, request):
        """Get in-progress user achievements"""
        in_progress_achievements = self.get_queryset().filter(completed_at__isnull=True)
        serializer = UserAchievementListSerializer(in_progress_achievements, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get achievement/challenge statistics using centralized XP system"""
        # Get counts
        total = UserAchievement.objects.filter(user=request.user).count()
        completed = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=False
        ).count()
        
        # Get completion rate by type
        completion_by_type = {}
        for type_code, type_name in Achievement.TYPES:
            type_total = UserAchievement.objects.filter(
                user=request.user,
                achievement__type=type_code
            ).count()
            
            type_completed = UserAchievement.objects.filter(
                user=request.user,
                achievement__type=type_code,
                completed_at__isnull=False
            ).count()
            
            if type_total > 0:
                completion_rate = (type_completed / type_total) * 100
            else:
                completion_rate = 0
                
            completion_by_type[type_code] = {
                'name': type_name,
                'total': type_total,
                'completed': type_completed,
                'completion_rate': round(completion_rate, 2)
            }
        
        # Get recent completions
        recent_completions = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=False
        ).order_by('-completed_at')[:5]
        
        recent_serializer = UserAchievementListSerializer(recent_completions, many=True)
        
        # Use centralized XP calculator
        total_xp = XPCalculator.get_user_total_xp(request.user)
        xp_breakdown = XPCalculator.get_user_xp_breakdown(request.user)
        
        return Response({
            'total_achievements': total,
            'completed_achievements': completed,
            'completion_rate': round((completed / total * 100), 2) if total > 0 else 0,
            'completion_by_type': completion_by_type,
            'recent_completions': recent_serializer.data,
            'total_xp': total_xp,
            'xp_breakdown': xp_breakdown
        })
    
    @action(detail=False, methods=['get'])
    def rank_info(self, request):
        """Get user's rank information using centralized XP system"""
        # Use centralized XP calculator
        level_info = XPCalculator.get_user_level_info(request.user)
        
        return Response({
            'current_rank': level_info['current_level'],
            'next_rank': level_info['next_level'],
            'total_xp': level_info['total_xp'],
            'progress_xp': level_info['progress_xp'],
            'required_xp': level_info['required_xp'],
            'progress_percentage': level_info['progress_percentage']
        })
    
    @action(detail=False, methods=['get'])
    def leaderboard(self, request):
        """Get XP-based leaderboard using centralized XP system"""
        limit = int(request.query_params.get('limit', 50))
        leaderboard_data = XPCalculator.get_leaderboard_data(limit)
        
        # Find current user's rank
        current_user_rank = None
        current_user_in_top = False
        
        for entry in leaderboard_data:
            if entry['user_id'] == request.user.id:
                current_user_rank = entry['rank']
                current_user_in_top = True
                break
        
        # If user not in top results, get their rank
        if not current_user_in_top:
            user_xp = XPCalculator.get_user_total_xp(request.user)
            users_above = UserAchievement.objects.filter(
                completed_at__isnull=False
            ).values('user').annotate(
                total_xp=Sum('achievement__xp_reward')
            ).filter(total_xp__gt=user_xp).count()
            
            current_user_rank = users_above + 1
            
            # Add current user to results
            user_level_info = XPCalculator.get_user_level_info(request.user)
            leaderboard_data.append({
                'rank': current_user_rank,
                'user_id': request.user.id,
                'username': request.user.username,
                'total_xp': user_level_info['total_xp'],
                'level': user_level_info['current_level']['level'],
                'badge_name': user_level_info['current_level']['name'],
                'is_current_user': True
            })
        
        return Response({
            'leaderboard': leaderboard_data,
            'current_user_rank': current_user_rank,
            'total_users': UserAchievement.objects.filter(
                completed_at__isnull=False
            ).values('user').distinct().count()
        })

    @action(detail=False, methods=['get'])
    def completed_by_category(self, request):
        """
        SUPER FAST endpoint for completed challenges by category
        
        Usage:
        GET /api/gamification/user-achievements/completed_by_category/?category=location
        GET /api/gamification/user-achievements/completed_by_category/?category=artist
        GET /api/gamification/user-achievements/completed_by_category/?category=genre
        GET /api/gamification/user-achievements/completed_by_category/?category=social
        """
        category = request.query_params.get('category')
        
        if not category:
            return Response(
                {'error': 'category parameter is required (location, artist, genre, social)'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if category not in ['location', 'artist', 'genre', 'social']:
            return Response(
                {'error': 'Invalid category. Must be: location, artist, genre, or social'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # NO CACHING - immediate updates for gamification feedback
        
        # Optimized query with select_related and only needed fields
        completed_challenges = UserAchievement.objects.filter(
            user=request.user,
            achievement__type=category,
            completed_at__isnull=False
        ).select_related('achievement').only(
            'id', 'completed_at', 'progress',
            'achievement__id', 'achievement__name', 'achievement__description',
            'achievement__xp_reward', 'achievement__icon_name', 'achievement__tier'
        ).order_by('-completed_at')
        
        # Minimal response data for speed
        response_data = []
        for ua in completed_challenges:
            response_data.append({
                'id': ua.id,
                'name': ua.achievement.name,
                'description': ua.achievement.description,
                'xp_reward': ua.achievement.xp_reward,
                'tier': ua.achievement.tier,
                'icon_name': ua.achievement.icon_name,
                'completed_at': ua.completed_at.isoformat() if ua.completed_at else None,
                'category': category
            })
        
        return Response(response_data)
    
    @action(detail=False, methods=['get'])
    def completed_summary(self, request):
        """
        ULTRA FAST endpoint for completed challenges summary by all categories
        
        Usage:
        GET /api/gamification/user-achievements/completed_summary/
        
        Returns count and recent completions for each category
        """
        # NO CACHING - immediate updates for gamification feedback
        
        # Single optimized query for all categories
        completed_by_category = UserAchievement.objects.filter(
            user=request.user,
            completed_at__isnull=False
        ).select_related('achievement').only(
            'id', 'completed_at', 
            'achievement__id', 'achievement__name', 'achievement__type',
            'achievement__xp_reward', 'achievement__tier'
        ).order_by('-completed_at')
        
        # Group by category
        summary = {
            'location': {'count': 0, 'recent': []},
            'artist': {'count': 0, 'recent': []},
            'genre': {'count': 0, 'recent': []},
            'social': {'count': 0, 'recent': []}
        }
        
        for ua in completed_by_category:
            category = ua.achievement.type
            if category in summary:
                summary[category]['count'] += 1
                if len(summary[category]['recent']) < 3:  # Only keep 3 most recent
                    summary[category]['recent'].append({
                        'id': ua.id,
                        'name': ua.achievement.name,
                        'completed_at': ua.completed_at.isoformat(),
                        'xp_reward': ua.achievement.xp_reward,
                        'tier': ua.achievement.tier
                    })
        
        return Response(summary)
    
    @action(detail=False, methods=['get'])
    def in_progress_by_category(self, request):
        """
        SUPER FAST endpoint for in-progress challenges by category
        
        Usage:
        GET /api/gamification/user-achievements/in_progress_by_category/?category=location
        GET /api/gamification/user-achievements/in_progress_by_category/?category=artist
        GET /api/gamification/user-achievements/in_progress_by_category/?category=genre
        GET /api/gamification/user-achievements/in_progress_by_category/?category=social
        """
        category = request.query_params.get('category')
        
        if not category:
            return Response(
                {'error': 'category parameter is required (location, artist, genre, social)'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if category not in ['location', 'artist', 'genre', 'social']:
            return Response(
                {'error': 'Invalid category. Must be: location, artist, genre, or social'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # NO CACHING - immediate updates for gamification feedback
        
        # Optimized query with select_related and only needed fields
        in_progress_challenges = UserAchievement.objects.filter(
            user=request.user,
            achievement__type=category,
            completed_at__isnull=True
        ).select_related('achievement').only(
            'id', 'progress', 'last_updated',
            'achievement__id', 'achievement__name', 'achievement__description',
            'achievement__xp_reward', 'achievement__icon_name', 'achievement__tier',
            'achievement__criteria'
        ).order_by('-last_updated')
        
        # Minimal response data for speed with progress calculation
        response_data = []
        for ua in in_progress_challenges:
            # Calculate progress percentage
            criteria = ua.achievement.criteria or {}
            progress = ua.progress or {}
            
            # Get the main completion criteria for the achievement
            required_count = 1  # Default
            current_count = 0
            
            # Determine required count based on criteria
            if 'required_count' in criteria:
                required_count = criteria['required_count']
            elif 'required_artists' in criteria:
                required_count = criteria['required_artists']
            elif 'required_genres' in criteria:
                required_count = criteria['required_genres']
            elif 'required_cities' in criteria:
                required_count = criteria['required_cities']
            elif 'required_votes' in criteria:
                required_count = criteria['required_votes']
            elif 'required_comments' in criteria:
                required_count = criteria['required_comments']
            
            # Determine current count based on progress
            if 'unique_artists' in progress:
                current_count = progress['unique_artists']
            elif 'unique_genres' in progress:
                current_count = progress['unique_genres']
            elif 'different_cities' in progress:
                current_count = progress['different_cities']
            elif 'votes_given' in progress:
                current_count = progress['votes_given']
            elif 'comments_made' in progress:
                current_count = progress['comments_made']
            elif 'total_pins' in progress:
                current_count = progress['total_pins']
            
            progress_percentage = min(100, (current_count / required_count * 100)) if required_count > 0 else 0
            
            response_data.append({
                'id': ua.id,
                'name': ua.achievement.name,
                'description': ua.achievement.description,
                'xp_reward': ua.achievement.xp_reward,
                'tier': ua.achievement.tier,
                'icon_name': ua.achievement.icon_name,
                'category': category,
                'progress': {
                    'current_count': current_count,
                    'required_count': required_count,
                    'progress_percentage': round(progress_percentage, 1)
                },
                'last_updated': ua.last_updated.isoformat() if ua.last_updated else None
            })
        
        return Response(response_data) 
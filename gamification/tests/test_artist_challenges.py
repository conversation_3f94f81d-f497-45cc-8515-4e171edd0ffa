from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.gis.geos import Point
from unittest.mock import patch, MagicMock

from pins.models import Pin
from music.models import Track
from gamification.models import Achievement, UserAchievement
from gamification.services.progress_tracker import ProgressTracker

User = get_user_model()


class ArtistChallengeTrackingTest(TestCase):
    """Test suite for artist challenge tracking functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test achievements with artist criteria
        self.artist_achievement_1 = Achievement.objects.create(
            name='Music Explorer',
            description='Discover 5 unique artists',
            criteria={'unique_artists': 5},
            type='artist',
            xp_reward=100
        )
        
        self.artist_achievement_2 = Achievement.objects.create(
            name='Artist Collector',
            description='Pin 10 unique artists',
            criteria={'unique_artists': 10},
            type='artist',
            xp_reward=200
        )
        
        self.artist_achievement_3 = Achievement.objects.create(
            name='Artist Devotee',
            description='Pin same artist 3 times',
            criteria={'same_artist_pins': 3},
            type='artist',
            xp_reward=150
        )
        
    def test_single_pin_single_artist_count(self):
        """Test that creating one pin with one artist only counts as 1 unique artist"""
        # Mock geocoding service to avoid external API calls
        with patch('pins.services.GeocodingService.reverse_geocode') as mock_geocode:
            mock_geocode.return_value = {
                'city': 'Test City',
                'state': 'Test State', 
                'country': 'Test Country',
                'continent': 'North America',
                'district': 'Test District',
                'population': 100000
            }
            
            # Create a pin with correct Pin model fields
            pin = Pin.objects.create(
                owner=self.user,
                title='Test Pin',
                track_title='Test Song',
                track_artist='Test Artist',
                album='Test Album',
                track_url='https://open.spotify.com/track/test123',
                service='spotify',
                genre='Pop',
                location=Point(-74.0060, 40.7128),  # NYC coordinates
                location_name='Test Location'
            )
            
            # Check progress for all artist achievements
            achievements = UserAchievement.objects.filter(
                user=self.user,
                achievement__in=[self.artist_achievement_1, self.artist_achievement_2, self.artist_achievement_3]
            )
            
            for user_achievement in achievements:
                progress = user_achievement.progress
                # Each achievement should show exactly 1 unique artist, not more
                self.assertEqual(progress.get('unique_artists', 0), 1, 
                    f"Achievement '{user_achievement.achievement.name}' shows {progress.get('unique_artists', 0)} unique artists, should be 1")
                self.assertEqual(len(progress.get('artists_played', [])), 1,
                    f"Achievement '{user_achievement.achievement.name}' has {len(progress.get('artists_played', []))} artists in list, should be 1")
                self.assertIn('Test Artist', progress.get('artists_played', []),
                    f"Achievement '{user_achievement.achievement.name}' missing 'Test Artist' in artists_played list")
    
    def test_multiple_pins_same_artist(self):
        """Test that multiple pins with same artist only count as 1 unique artist"""
        with patch('pins.services.GeocodingService.reverse_geocode') as mock_geocode:
            mock_geocode.return_value = {
                'city': 'Test City',
                'state': 'Test State',
                'country': 'Test Country', 
                'continent': 'North America',
                'district': 'Test District',
                'population': 100000
            }
            
            # Create multiple pins with same artist
            for i in range(3):
                Pin.objects.create(
                    owner=self.user,
                    title=f'Pin {i}',
                    track_title=f'Test Song {i}',
                    track_artist='Same Artist',
                    album=f'Test Album {i}',
                    track_url=f'https://open.spotify.com/track/test{i}',
                    service='spotify',
                    genre='Pop',
                    location=Point(-74.0060 + i*0.001, 40.7128 + i*0.001),
                    location_name=f'Test Location {i}'
                )
            
            # Check that we still have only 1 unique artist
            user_achievement = UserAchievement.objects.get(
                user=self.user,
                achievement=self.artist_achievement_1
            )
            progress = user_achievement.progress
            
            self.assertEqual(progress.get('unique_artists', 0), 1,
                f"Expected 1 unique artist after 3 pins with same artist, got {progress.get('unique_artists', 0)}")
            self.assertEqual(len(progress.get('artists_played', [])), 1,
                f"Expected 1 artist in list after 3 pins with same artist, got {len(progress.get('artists_played', []))}")
            
            # Check same_artist_pins count
            devotee_achievement = UserAchievement.objects.get(
                user=self.user,
                achievement=self.artist_achievement_3
            )
            devotee_progress = devotee_achievement.progress
            
            self.assertEqual(devotee_progress.get('same_artist_pins', 0), 3,
                f"Expected 3 same artist pins, got {devotee_progress.get('same_artist_pins', 0)}")
    
    def test_multiple_pins_different_artists(self):
        """Test that pins with different artists correctly count unique artists"""
        artists = ['Artist One', 'Artist Two', 'Artist Three', 'Artist Four', 'Artist Five']
        
        with patch('pins.services.GeocodingService.reverse_geocode') as mock_geocode:
            mock_geocode.return_value = {
                'city': 'Test City',
                'state': 'Test State',
                'country': 'Test Country',
                'continent': 'North America', 
                'district': 'Test District',
                'population': 100000
            }
            
            # Create pins with different artists
            for i, artist in enumerate(artists):
                Pin.objects.create(
                    owner=self.user,
                    title=f'Pin {i}',
                    track_title=f'Song by {artist}',
                    track_artist=artist,
                    album=f'Album by {artist}',
                    track_url=f'https://open.spotify.com/track/track_{i}',
                    service='spotify',
                    genre='Pop',
                    location=Point(-74.0060 + i*0.001, 40.7128 + i*0.001),
                    location_name=f'Location {i}'
                )
            
            # Check progress - should have exactly 5 unique artists
            user_achievement = UserAchievement.objects.get(
                user=self.user,
                achievement=self.artist_achievement_1
            )
            progress = user_achievement.progress
            
            self.assertEqual(progress.get('unique_artists', 0), 5,
                f"Expected 5 unique artists, got {progress.get('unique_artists', 0)}")
            self.assertEqual(len(progress.get('artists_played', [])), 5,
                f"Expected 5 artists in list, got {len(progress.get('artists_played', []))}")
            
            # Verify all artists are tracked
            artists_played = set(progress.get('artists_played', []))
            expected_artists = set(artists)
            self.assertEqual(artists_played, expected_artists,
                f"Artists played {artists_played} doesn't match expected {expected_artists}")
            
            # Check that first achievement should be completed (requires 5 unique artists)
            self.assertTrue(user_achievement.completed_at is not None,
                "Music Explorer achievement should be completed after 5 unique artists")
    
    def test_progress_consistency_across_achievements(self):
        """Test that artist progress is consistent across all achievements"""
        with patch('pins.services.GeocodingService.reverse_geocode') as mock_geocode:
            mock_geocode.return_value = {
                'city': 'Test City',
                'state': 'Test State',
                'country': 'Test Country',
                'continent': 'North America',
                'district': 'Test District',
                'population': 100000
            }
            
            # Create 3 pins with 2 unique artists (Artist A twice, Artist B once)
            pins_data = [
                ('Artist A', 'Song 1'),
                ('Artist A', 'Song 2'), 
                ('Artist B', 'Song 3')
            ]
            
            for i, (artist, song) in enumerate(pins_data):
                Pin.objects.create(
                    owner=self.user,
                    title=f'Pin {i}',
                    track_title=song,
                    track_artist=artist,
                    album=f'Album {i}',
                    track_url=f'https://open.spotify.com/track/track_{i}',
                    service='spotify',
                    genre='Pop',
                    location=Point(-74.0060 + i*0.001, 40.7128 + i*0.001),
                    location_name=f'Location {i}'
                )
            
            # Get all artist-related achievements
            achievements = UserAchievement.objects.filter(
                user=self.user,
                achievement__in=[self.artist_achievement_1, self.artist_achievement_2, self.artist_achievement_3]
            )
            
            # All achievements should show consistent artist data
            for user_achievement in achievements:
                progress = user_achievement.progress
                
                # Should have exactly 2 unique artists
                self.assertEqual(progress.get('unique_artists', 0), 2,
                    f"Achievement '{user_achievement.achievement.name}' shows {progress.get('unique_artists', 0)} unique artists, should be 2")
                
                # Should have both artists in the list
                artists_played = set(progress.get('artists_played', []))
                expected_artists = {'Artist A', 'Artist B'}
                self.assertEqual(artists_played, expected_artists,
                    f"Achievement '{user_achievement.achievement.name}' has inconsistent artists_played: {artists_played} vs expected {expected_artists}")
                
                # Artist A should have 2 pins
                artist_pin_counts = progress.get('artist_pin_counts', {})
                self.assertEqual(artist_pin_counts.get('Artist A', 0), 2,
                    f"Achievement '{user_achievement.achievement.name}' shows {artist_pin_counts.get('Artist A', 0)} pins for Artist A, should be 2")
                self.assertEqual(artist_pin_counts.get('Artist B', 0), 1,
                    f"Achievement '{user_achievement.achievement.name}' shows {artist_pin_counts.get('Artist B', 0)} pins for Artist B, should be 1")
                
                # same_artist_pins should be 2 (max pins for any single artist)
                self.assertEqual(progress.get('same_artist_pins', 0), 2,
                    f"Achievement '{user_achievement.achievement.name}' shows {progress.get('same_artist_pins', 0)} same artist pins, should be 2")
    
    def test_direct_progress_tracker_call(self):
        """Test calling ProgressTracker directly with pin data"""
        pin_data = {
            'current_pin_id': 1,
            'latitude': 40.7128,
            'longitude': -74.0060,
            'city': 'Test City',
            'state': 'Test State',
            'country': 'Test Country',
            'continent': 'North America',
            'district': 'Test District',
            'population': 100000,
            'location_name': 'Test Location',
            'artist_id': None,
            'artist_name': 'Test Artist',
            'album_id': None,
            'album_name': 'Test Album',
            'genre': 'Pop',
            'service': 'spotify',
            'created_at': timezone.now(),
            'time_of_day': 12,
            'is_public': True
        }
        
        # Call ProgressTracker directly
        ProgressTracker.update_pin_progress(self.user, pin_data)
        
        # Verify that all achievements show consistent data
        achievements = UserAchievement.objects.filter(
            user=self.user,
            achievement__type='artist'
        )
        
        for user_achievement in achievements:
            progress = user_achievement.progress
            self.assertEqual(progress.get('unique_artists', 0), 1,
                f"Direct call: Achievement '{user_achievement.achievement.name}' shows {progress.get('unique_artists', 0)} unique artists, should be 1")
            self.assertIn('Test Artist', progress.get('artists_played', []),
                f"Direct call: Achievement '{user_achievement.achievement.name}' missing 'Test Artist' in list") 
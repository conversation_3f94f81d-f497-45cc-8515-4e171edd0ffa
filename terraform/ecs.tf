# ECR Repositories
resource "aws_ecr_repository" "app" {
  name                 = "${var.project_name}-${var.environment}"
  image_tag_mutability = "MUTABLE"
  force_delete         = true

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = local.common_tags
}

# Note: Tileserver ECR repository removed - using B2 upload instead

# ECR Lifecycle Policies
resource "aws_ecr_lifecycle_policy" "app" {
  repository = aws_ecr_repository.app.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 10 production images"
        selection = {
          tagStatus     = "tagged"
          tagPrefixList = ["prod"]
          countType     = "imageCountMoreThan"
          countNumber   = 10
        }
        action = {
          type = "expire"
        }
      },
      {
        rulePriority = 2
        description  = "Keep last 5 staging images"
        selection = {
          tagStatus     = "tagged"
          tagPrefixList = ["staging"]
          countType     = "imageCountMoreThan"
          countNumber   = 5
        }
        action = {
          type = "expire"
        }
      },
      {
        rulePriority = 3
        description  = "Delete untagged images older than 1 day"
        selection = {
          tagStatus   = "untagged"
          countType   = "sinceImagePushed"
          countUnit   = "days"
          countNumber = 1
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# ECS Cluster
resource "aws_ecs_cluster" "main" {
  name = "${var.project_name}-${var.environment}"

  configuration {
    execute_command_configuration {
      logging = "OVERRIDE"
      log_configuration {
        cloud_watch_log_group_name = aws_cloudwatch_log_group.ecs.name
      }
    }
  }

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = local.common_tags
}

# ECS Capacity Provider
resource "aws_ecs_cluster_capacity_providers" "main" {
  cluster_name = aws_ecs_cluster.main.name

  capacity_providers = ["FARGATE", "FARGATE_SPOT"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = var.use_spot_instances ? "FARGATE_SPOT" : "FARGATE"
  }
}

# CloudWatch Log Groups
resource "aws_cloudwatch_log_group" "ecs" {
  name              = "/ecs/${var.project_name}-${var.environment}"
  retention_in_days = 30

  tags = local.common_tags
}

resource "aws_cloudwatch_log_group" "app" {
  name              = "/ecs/${var.project_name}-${var.environment}/app"
  retention_in_days = 14

  tags = local.common_tags
}

resource "aws_cloudwatch_log_group" "celery" {
  name              = "/ecs/${var.project_name}-${var.environment}/celery"
  retention_in_days = 7

  tags = local.common_tags
}

# Note: Tileserver log group removed - using B2 upload instead

# Application Load Balancer
resource "aws_lb" "main" {
  name               = "${var.project_name}-${var.environment}-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = aws_subnet.public[*].id

  enable_deletion_protection = var.enable_deletion_protection

  access_logs {
    bucket  = aws_s3_bucket.static.id
    prefix  = "alb-logs"
    enabled = true
  }

  tags = local.common_tags
}

# Target Groups
resource "aws_lb_target_group" "app" {
  name        = "${var.project_name}-${var.environment}-app"
  port        = var.app_port
  protocol    = "HTTP"
  vpc_id      = aws_vpc.main.id
  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200,301"
    path                = "/health/"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 10
    unhealthy_threshold = 3
  }

  tags = local.common_tags
}

# Note: Tileserver target group removed - using B2 upload instead

# ALB Listeners
resource "aws_lb_listener" "app" {
  load_balancer_arn = aws_lb.main.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "app_https" {
  load_balancer_arn = aws_lb.main.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  certificate_arn   = aws_acm_certificate_validation.cert.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.app.arn
  }
}

# Note: Tileserver ALB rules removed - using B2 upload instead

# Task Definition for Django App
resource "aws_ecs_task_definition" "app" {
  family                   = "${var.project_name}-${var.environment}-app"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.app_cpu
  memory                   = var.app_memory
  execution_role_arn       = aws_iam_role.ecs_task_execution.arn
  task_role_arn            = aws_iam_role.ecs_task.arn

  container_definitions = jsonencode([
    {
      name  = "app"
      image = "${aws_ecr_repository.app.repository_url}:${var.app_image_tag}"
      
      portMappings = [
        {
          containerPort = var.app_port
          protocol      = "tcp"
        }
      ]

      environment = [
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "DEBUG"
          value = "False"
        },
        {
          name  = "B2_APPLICATION_KEY_ID"
          value = var.b2_application_key_id
        },
        {
          name  = "B2_APPLICATION_KEY"
          value = var.b2_application_key
        },
        {
          name  = "B2_BUCKET_NAME"
          value = b2_bucket.static.bucket_name
        },
        {
          name  = "B2_ENDPOINT_URL"
          value = local.b2_s3_endpoint
        },
        {
          name  = "B2_REGION"
          value = var.b2_region
        },
        {
          name  = "MEDIA_BUCKET_NAME"
          value = b2_bucket.media.bucket_name
        },
        {
          name  = "B2_TILES_BUCKET_NAME"
          value = b2_bucket.tiles.bucket_name
        },
        {
          name  = "USE_S3_FOR_MEDIA"
          value = "True"
        },
        {
          name  = "REDIS_URL"
          value = "redis://:${random_password.redis_auth.result}@${aws_elasticache_replication_group.redis.primary_endpoint_address}:6379/0"
        },
        {
          name  = "COLLECT_STATIC"
          value = "true"
        },
        {
          name  = "SPOTIFY_CLIENT_ID"
          value = var.spotify_client_id
        },
        {
          name  = "SPOTIFY_CLIENT_SECRET"
          value = var.spotify_client_secret
        },
        {
          name  = "SPOTIFY_REDIRECT_URI"
          value = var.spotify_redirect_uri
        },
        {
          name  = "SPOTIFY_MOBILE_REDIRECT_URI"
          value = var.spotify_mobile_redirect_uri
        },
        {
          name  = "ALLOWED_REDIRECT_URIS"
          value = var.allowed_redirect_uris
        },
        {
          name  = "LASTFM_API_KEY"
          value = var.lastfm_api_key
        },
        {
          name  = "APPLE_MUSIC_KEY_ID"
          value = var.apple_music_key_id
        },
        {
          name  = "APPLE_MUSIC_TEAM_ID"
          value = var.apple_music_team_id
        },
        {
          name  = "APPLE_MUSIC_PRIVATE_KEY"
          value = var.apple_music_private_key
        },
        {
          name  = "SOUNDCLOUD_CLIENT_ID"
          value = var.soundcloud_client_id
        },
        {
          name  = "SOUNDCLOUD_CLIENT_SECRET"
          value = var.soundcloud_client_secret
        },
        {
          name  = "ONESIGNAL_APP_ID"
          value = var.onesignal_app_id
        },
        {
          name  = "ONESIGNAL_API_KEY"
          value = var.onesignal_api_key
        },
        {
          name  = "ONESIGNAL_API_URL"
          value = var.onesignal_api_url
        },
        {
          name  = "JWT_ACCESS_TOKEN_LIFETIME"
          value = tostring(var.jwt_access_token_lifetime)
        },
        {
          name  = "JWT_REFRESH_TOKEN_LIFETIME"
          value = tostring(var.jwt_refresh_token_lifetime)
        },
        {
          name  = "CORS_ALLOWED_ORIGINS"
          value = var.cors_allowed_origins
        },
        {
          name  = "CSRF_TRUSTED_ORIGINS"
          value = var.csrf_trusted_origins
        },
        {
          name  = "EMAIL_BACKEND"
          value = var.email_backend
        },
        {
          name  = "EMAIL_HOST"
          value = var.email_host
        },
        {
          name  = "EMAIL_PORT"
          value = tostring(var.email_port)
        },
        {
          name  = "EMAIL_HOST_USER"
          value = var.email_host_user
        },
        {
          name  = "EMAIL_HOST_PASSWORD"
          value = var.email_host_password
        },
        {
          name  = "EMAIL_USE_TLS"
          value = tostring(var.email_use_tls)
        },
        {
          name  = "DEFAULT_FROM_EMAIL"
          value = var.default_from_email
        },
        {
          name  = "SERVER_EMAIL"
          value = var.server_email
        },
        {
          name  = "SECURE_SSL_REDIRECT"
          value = tostring(var.secure_ssl_redirect)
        },
        {
          name  = "SES_CONFIGURATION_SET"
          value = var.ses_configuration_set_name
        },
        {
          name  = "AWS_REGION"
          value = var.aws_region
        },
        {
          name  = "APPLE_BUNDLE_ID"
          value = var.apple_bundle_id
        },
        {
          name  = "APPLE_TEAM_ID"
          value = var.apple_team_id
        },
        {
          name  = "SOUNDCHARTS_API_KEY"
          value = var.soundcharts_api_key
        },
        {
          name  = "SOUNDCHARTS_APP_ID"
          value = var.soundcharts_app_id
        },
        {
          name  = "GOOGLE_PLACES_API_KEY"
          value = var.google_places_api_key
        },
        {
          name  = "GROQ_API_KEY"
          value = var.groq_api_key
        },
        {
          name  = "YOUTUBE_API_KEY"
          value = var.youtube_api_key
        },
        {
          name  = "MEDIA_STORAGE"
          value = var.media_storage
        },
        {
          name  = "MEDIA_URL"
          value = var.media_url
        },
        {
          name  = "STATIC_URL"
          value = var.static_url
        },
        {
          name  = "ALLOWED_HOSTS"
          value = var.allowed_hosts
        }
      ]

      secrets = [
        {
          name      = "SECRET_KEY"
          valueFrom = aws_secretsmanager_secret.django_secret.arn
        },
        {
          name      = "DATABASE_URL"
          valueFrom = aws_secretsmanager_secret.database_url.arn
        }
      ]

      mountPoints = []

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.app.name
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "ecs"
        }
      }

      healthCheck = {
        command = [
          "CMD-SHELL",
          "curl -f http://localhost:${var.app_port}/health/ || exit 1"
        ]
        interval    = 30
        timeout     = 5
        retries     = 3
        startPeriod = 60
      }

      essential = true
    }
  ])

  tags = local.common_tags
}

# Task Definition for Celery Worker
resource "aws_ecs_task_definition" "celery" {
  family                   = "${var.project_name}-${var.environment}-celery"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.celery_cpu
  memory                   = var.celery_memory
  execution_role_arn       = aws_iam_role.ecs_task_execution.arn
  task_role_arn            = aws_iam_role.ecs_task.arn

  container_definitions = jsonencode([
    {
      name    = "celery-worker"
      image   = "${aws_ecr_repository.app.repository_url}:${var.app_image_tag}"
      command = ["celery", "-A", "bopmaps", "worker", "-l", "INFO"]

      environment = [
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "DEBUG"
          value = "False"
        },
        {
          name  = "B2_APPLICATION_KEY_ID"
          value = var.b2_application_key_id
        },
        {
          name  = "B2_APPLICATION_KEY"
          value = var.b2_application_key
        },
        {
          name  = "B2_BUCKET_NAME"
          value = b2_bucket.static.bucket_name
        },
        {
          name  = "B2_ENDPOINT_URL"
          value = local.b2_s3_endpoint
        },
        {
          name  = "B2_REGION"
          value = var.b2_region
        },
        {
          name  = "MEDIA_BUCKET_NAME"
          value = b2_bucket.media.bucket_name
        },
        {
          name  = "B2_TILES_BUCKET_NAME"
          value = b2_bucket.tiles.bucket_name
        },
        {
          name  = "USE_S3_FOR_MEDIA"
          value = "True"
        },
        {
          name  = "REDIS_URL"
          value = "redis://:${random_password.redis_auth.result}@${aws_elasticache_replication_group.redis.primary_endpoint_address}:6379/0"
        },
        {
          name  = "SPOTIFY_CLIENT_ID"
          value = var.spotify_client_id
        },
        {
          name  = "SPOTIFY_CLIENT_SECRET"
          value = var.spotify_client_secret
        },
        {
          name  = "SPOTIFY_REDIRECT_URI"
          value = var.spotify_redirect_uri
        },
        {
          name  = "SPOTIFY_MOBILE_REDIRECT_URI"
          value = var.spotify_mobile_redirect_uri
        },
        {
          name  = "LASTFM_API_KEY"
          value = var.lastfm_api_key
        },
        {
          name  = "SOUNDCLOUD_CLIENT_ID"
          value = var.soundcloud_client_id
        },
        {
          name  = "SOUNDCLOUD_CLIENT_SECRET"
          value = var.soundcloud_client_secret
        },
        {
          name  = "APPLE_MUSIC_KEY_ID"
          value = var.apple_music_key_id
        },
        {
          name  = "APPLE_MUSIC_PRIVATE_KEY"
          value = var.apple_music_private_key
        },
        {
          name  = "APPLE_MUSIC_TEAM_ID"
          value = var.apple_music_team_id
        },
        {
          name  = "ONESIGNAL_APP_ID"
          value = var.onesignal_app_id
        },
        {
          name  = "ONESIGNAL_API_KEY"
          value = var.onesignal_api_key
        },
        {
          name  = "ONESIGNAL_API_URL"
          value = var.onesignal_api_url
        },
        {
          name  = "CORS_ALLOWED_ORIGINS"
          value = var.cors_allowed_origins
        },
        {
          name  = "CSRF_TRUSTED_ORIGINS"
          value = var.csrf_trusted_origins
        },
        {
          name  = "ALLOWED_REDIRECT_URIS"
          value = var.allowed_redirect_uris
        },
        {
          name  = "JWT_ACCESS_TOKEN_LIFETIME"
          value = tostring(var.jwt_access_token_lifetime)
        },
        {
          name  = "JWT_REFRESH_TOKEN_LIFETIME"
          value = tostring(var.jwt_refresh_token_lifetime)
        },
        {
          name  = "EMAIL_BACKEND"
          value = var.email_backend
        },
        {
          name  = "EMAIL_HOST"
          value = var.email_host
        },
        {
          name  = "EMAIL_PORT"
          value = tostring(var.email_port)
        },
        {
          name  = "EMAIL_HOST_USER"
          value = var.email_host_user
        },
        {
          name  = "EMAIL_HOST_PASSWORD"
          value = var.email_host_password
        },
        {
          name  = "EMAIL_USE_TLS"
          value = tostring(var.email_use_tls)
        },
        {
          name  = "DEFAULT_FROM_EMAIL"
          value = var.default_from_email
        },
        {
          name  = "SERVER_EMAIL"
          value = var.server_email
        },
        {
          name  = "SECURE_SSL_REDIRECT"
          value = tostring(var.secure_ssl_redirect)
        },
        {
          name  = "SES_CONFIGURATION_SET"
          value = var.ses_configuration_set_name
        },
        {
          name  = "AWS_REGION"
          value = var.aws_region
        },
        {
          name  = "APPLE_BUNDLE_ID"
          value = var.apple_bundle_id
        },
        {
          name  = "APPLE_TEAM_ID"
          value = var.apple_team_id
        },
        {
          name  = "SOUNDCHARTS_API_KEY"
          value = var.soundcharts_api_key
        },
        {
          name  = "SOUNDCHARTS_APP_ID"
          value = var.soundcharts_app_id
        },
        {
          name  = "GOOGLE_PLACES_API_KEY"
          value = var.google_places_api_key
        },
        {
          name  = "GROQ_API_KEY"
          value = var.groq_api_key
        },
        {
          name  = "YOUTUBE_API_KEY"
          value = var.youtube_api_key
        },
        {
          name  = "MEDIA_STORAGE"
          value = var.media_storage
        },
        {
          name  = "MEDIA_URL"
          value = var.media_url
        },
        {
          name  = "STATIC_URL"
          value = var.static_url
        },
        {
          name  = "ALLOWED_HOSTS"
          value = var.allowed_hosts
        }
      ]

      secrets = [
        {
          name      = "SECRET_KEY"
          valueFrom = aws_secretsmanager_secret.django_secret.arn
        },
        {
          name      = "DATABASE_URL"
          valueFrom = aws_secretsmanager_secret.database_url.arn
        }
      ]

      mountPoints = []

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.celery.name
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "ecs"
        }
      }

      essential = true
    }
  ])

  tags = local.common_tags
}

# Task Definition for Planet Data Processing
resource "aws_ecs_task_definition" "planet_processor" {
  family                   = "${var.project_name}-${var.environment}-planet-processor"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "4096"  # 4 vCPUs for heavy processing
  memory                   = "16384" # 16 GB RAM for planet processing
  execution_role_arn       = aws_iam_role.ecs_task_execution.arn
  task_role_arn            = aws_iam_role.ecs_task.arn

  container_definitions = jsonencode([
    {
      name  = "planet-processor"
      image = "${aws_ecr_repository.app.repository_url}:${var.app_image_tag}"
      
      # Override command for planet processing
      command = [
        "python", "manage.py", "process_planet_tiles", 
        "--area=planet", "--zoom-levels=0-14"
      ]

      environment = [
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "DEBUG"
          value = "False"
        },
        {
          name  = "B2_APPLICATION_KEY_ID"
          value = var.b2_application_key_id
        },
        {
          name  = "B2_APPLICATION_KEY"
          value = var.b2_application_key
        },
        {
          name  = "B2_BUCKET_NAME"
          value = b2_bucket.static.bucket_name
        },
        {
          name  = "B2_TILES_BUCKET_NAME"
          value = b2_bucket.tiles.bucket_name
        },
        {
          name  = "B2_ENDPOINT_URL"
          value = local.b2_s3_endpoint
        },
        {
          name  = "B2_REGION"
          value = var.b2_region
        },
        {
          name  = "MEDIA_BUCKET_NAME"
          value = b2_bucket.media.bucket_name
        },
        {
          name  = "USE_S3_FOR_MEDIA"
          value = "True"
        },
        {
          name  = "REDIS_URL"
          value = "redis://:${random_password.redis_auth.result}@${aws_elasticache_replication_group.redis.primary_endpoint_address}:6379/0"
        }
      ]

      secrets = [
        {
          name      = "SECRET_KEY"
          valueFrom = aws_secretsmanager_secret.django_secret.arn
        },
        {
          name      = "DATABASE_URL"
          valueFrom = aws_secretsmanager_secret.database_url.arn
        }
      ]

      mountPoints = []

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.planet_processor.name
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "ecs"
        }
      }

      essential = true
    }
  ])

  tags = local.common_tags
}

# CloudWatch Log Group for Planet Processor
resource "aws_cloudwatch_log_group" "planet_processor" {
  name              = "/ecs/${var.project_name}-${var.environment}/planet-processor"
  retention_in_days = 30

  tags = local.common_tags
}

# Task Definition for Database Migrations
resource "aws_ecs_task_definition" "migration" {
  family                   = "${var.project_name}-${var.environment}-migration"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = 512
  memory                   = 1024
  execution_role_arn       = aws_iam_role.ecs_task_execution.arn
  task_role_arn            = aws_iam_role.ecs_task.arn

  container_definitions = jsonencode([
    {
      name  = "migration"
      image = "${aws_ecr_repository.app.repository_url}:${var.app_image_tag}"
      
      # Override command for migrations
      command = [
        "sh", "-c", 
        "python manage.py migrate --noinput && python manage.py create_default_skin && echo 'Migrations completed successfully'"
      ]

      environment = [
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "DEBUG"
          value = "False"
        },
        {
          name  = "B2_APPLICATION_KEY_ID"
          value = var.b2_application_key_id
        },
        {
          name  = "B2_APPLICATION_KEY"
          value = var.b2_application_key
        },
        {
          name  = "B2_BUCKET_NAME"
          value = b2_bucket.static.bucket_name
        },
        {
          name  = "B2_ENDPOINT_URL"
          value = local.b2_s3_endpoint
        },
        {
          name  = "B2_REGION"
          value = var.b2_region
        },
        {
          name  = "MEDIA_BUCKET_NAME"
          value = b2_bucket.media.bucket_name
        },
        {
          name  = "USE_S3_FOR_MEDIA"
          value = "True"
        },
        {
          name  = "REDIS_URL"
          value = "redis://:${random_password.redis_auth.result}@${aws_elasticache_replication_group.redis.primary_endpoint_address}:6379/0"
        }
      ]

      secrets = [
        {
          name      = "SECRET_KEY"
          valueFrom = aws_secretsmanager_secret.django_secret.arn
        },
        {
          name      = "DATABASE_URL"
          valueFrom = aws_secretsmanager_secret.database_url.arn
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.migration.name
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "ecs"
        }
      }

      essential = true
    }
  ])

  tags = local.common_tags
}

# CloudWatch Log Group for Migration
resource "aws_cloudwatch_log_group" "migration" {
  name              = "/ecs/${var.project_name}-${var.environment}/migration"
  retention_in_days = 7

  tags = local.common_tags
}

# ECS Services
resource "aws_ecs_service" "app" {
  name            = "${var.project_name}-${var.environment}-app"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.app.arn
  desired_count   = var.app_count
  enable_execute_command = true

  capacity_provider_strategy {
    capacity_provider = var.use_spot_instances ? "FARGATE_SPOT" : "FARGATE"
    weight            = 100
  }

  network_configuration {
    security_groups  = [aws_security_group.ecs.id]
    subnets          = aws_subnet.private[*].id
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.app.arn
    container_name   = "app"
    container_port   = var.app_port
  }

  service_registries {
    registry_arn = aws_service_discovery_service.app.arn
  }

  depends_on = [aws_lb_listener.app_https]

  tags = local.common_tags
}

resource "aws_ecs_service" "celery" {
  name            = "${var.project_name}-${var.environment}-celery"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.celery.arn
  desired_count   = var.celery_count
  enable_execute_command = true

  capacity_provider_strategy {
    capacity_provider = var.use_spot_instances ? "FARGATE_SPOT" : "FARGATE"
    weight            = 100
  }

  network_configuration {
    security_groups  = [aws_security_group.ecs.id]
    subnets          = aws_subnet.private[*].id
    assign_public_ip = false
  }

  tags = local.common_tags
}

# Note: Tileserver service removed - using B2 upload instead

# Service Discovery
resource "aws_service_discovery_private_dns_namespace" "main" {
  name = "${var.project_name}-${var.environment}.local"
  vpc  = aws_vpc.main.id

  tags = local.common_tags
}

resource "aws_service_discovery_service" "app" {
  name = "app"

  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.main.id

    dns_records {
      ttl  = 10
      type = "A"
    }

    routing_policy = "MULTIVALUE"
  }

  tags = local.common_tags
}

# Secrets for Django
resource "aws_secretsmanager_secret" "django_secret" {
  name                    = "${var.project_name}-${var.environment}-django-secret"
  description             = "Django secret key for ${var.project_name} ${var.environment}"
  recovery_window_in_days = 7

  tags = local.common_tags
}

resource "aws_secretsmanager_secret_version" "django_secret" {
  secret_id     = aws_secretsmanager_secret.django_secret.id
  secret_string = var.django_secret_key
}

resource "aws_secretsmanager_secret" "database_url" {
  name                    = "${var.project_name}-${var.environment}-database-url"
  description             = "Database URL for ${var.project_name} ${var.environment}"
  recovery_window_in_days = 7

  tags = local.common_tags
}

# Database URL secret version with proper PostGIS driver
resource "aws_secretsmanager_secret_version" "database_url" {
  secret_id = aws_secretsmanager_secret.database_url.id
  secret_string = "postgis://${aws_db_instance.main.username}:${urlencode(random_password.db_password.result)}@${aws_db_instance.main.endpoint}/${aws_db_instance.main.db_name}"
  
  lifecycle {
    ignore_changes = [secret_string]
  }
} 
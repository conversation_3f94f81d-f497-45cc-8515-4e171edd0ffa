from django.shortcuts import render
from django.db import transaction, IntegrityError
from django.contrib.gis.geos import Point
from django.utils import timezone
from django.db import models
from django.db.models import Count
from rest_framework import status, viewsets, mixins
from rest_framework.response import Response
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from datetime import timedelta
from django.conf import settings
from django.db.models import Q

from .models import (
    Pin, PinInteraction, Collection, CollectionPin, VirtualPin, VirtualPinInteraction,
    PinSkin, UserSkin, ChallengeParticipation
)
from .serializers import (
    PinSerializer, PinGeoSerializer, PinInteractionSerializer, 
    CollectionSerializer, CollectionDetailSerializer, CollectionPinSerializer,
    VirtualPinSerializer, VirtualPinInteractionSerializer,
    PinSkinSerializer, UserSkinSerializer, WeeklyChallengeSerializer, 
    ChallengeParticipationSerializer
)
from .utils import get_nearby_pins, record_pin_interaction, get_trending_pins, check_pin_visibility, get_clustered_pins
from seeding.services import SeedingService

from bopmaps.views import BaseModelViewSet, BaseReadOnlyViewSet
from bopmaps.permissions import IsOwnerOrReadOnly
from bopmaps.utils import create_error_response
import logging
from challenges.models import WeeklyChallenge

# Import notification utilities
from notifications.utils import (
    notify_pin_liked, notify_friend_nearby, notify_pin_trending,
    check_and_notify_nearby_friends, notify_collection_collaboration,
    notify_fresh_pins_discovered
)

from friends.models import Friend

logger = logging.getLogger('bopmaps')

class PinViewSet(BaseModelViewSet):
    """
    API viewset for Pin CRUD operations
    """
    queryset = Pin.objects.all()
    serializer_class = PinSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReadOnly]
    
    def get_permissions(self):
        """
        Override permissions for voting actions.
        Voting should be allowed for any authenticated user on any visible pin.
        """
        if self.action in ['vote_post', 'vote_info', 'comment_post', 'comment_info', 'collect', 'view']:
            # Only require authentication for voting actions
            permission_classes = [IsAuthenticated]
        else:
            # Use default permissions for other actions
            permission_classes = [IsAuthenticated, IsOwnerOrReadOnly]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        if self.action == 'list_map' or self.action == 'nearby':
            return PinGeoSerializer
        return PinSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter expired pins
        queryset = queryset.filter(
            models.Q(expiration_date__isnull=True) | 
            models.Q(expiration_date__gt=timezone.now())
        )
        
        # Filter private pins (only show user's own private pins)
        if self.action in ['list', 'list_map', 'nearby']:
            queryset = queryset.filter(
                models.Q(is_private=False) | 
                models.Q(owner=self.request.user)
            )
        
        return queryset
    
    def perform_create(self, serializer):
        """
        Set the owner when creating a pin and trigger nearby friend notifications
        """
        pin = serializer.save(owner=self.request.user)
        
        # Check for nearby friends and notify them (geo-fenced, max 1/day)
        if pin.location:
            try:
                check_and_notify_nearby_friends(
                    user=self.request.user,
                    location_point=pin.location,
                    radius_km=5.0
                )
            except Exception as e:
                logger.error(f"Failed to check nearby friends for pin {pin.id}: {str(e)}")
        
        logger.info(f"Created pin: {self.request.user.username} created pin {pin.id}")
    
    def retrieve(self, request, *args, **kwargs):
        """
        Custom retrieve to check pin visibility before returning detail.
        Returns 404 if the pin is private and not owned by the requesting user.
        """
        try:
            instance = self.get_object()
            # Check if the pin should be visible to this user
            if instance.is_private and instance.owner != request.user:
                return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving pin: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def list_map(self, request):
        """
        Get pins for map display, optimized for performance with clustering
        """
        try:
            queryset = self.get_queryset()
            lat = request.query_params.get('latitude')
            lng = request.query_params.get('longitude')
            radius = request.query_params.get('radius', 1000)
            zoom = request.query_params.get('zoom', 13)
            
            try:
                zoom = int(zoom)
                radius = int(radius)
                
                # Dynamically adjust radius based on zoom level
                if zoom < 10:
                    max_radius = 10000
                elif zoom < 13:
                    max_radius = 5000
                else:
                    max_radius = 3000
                
                if radius > max_radius:
                    radius = max_radius
                    
            except (ValueError, TypeError):
                radius = 1000
                zoom = 13
                
            # If location is provided, use clustering approach
            if lat and lng:
                try:
                    result = get_clustered_pins(
                        user=request.user,
                        lat=float(lat),
                        lng=float(lng),
                        zoom=zoom,
                        radius_meters=radius
                    )
                    pins = result['pins']
                    
                    # Add cluster parameters to response metadata
                    cluster_params = result['cluster_params']
                    
                    # Enhanced visualization settings for clusters
                    cluster_params.update({
                        'visualization': {
                            'cluster_colors': {
                                'small': '#3388ff',  # Default blue for small clusters
                                'medium': '#ff8833',  # Orange for medium clusters
                                'large': '#ff3333'    # Red for large clusters
                            },
                            'cluster_sizes': {
                                'small': {'min': 2, 'max': 10},
                                'medium': {'min': 11, 'max': 50},
                                'large': {'min': 51, 'max': None}
                            },
                            'cluster_styles': {
                                'border_color': '#ffffff',
                                'border_width': 2,
                                'text_color': '#ffffff',
                                'font_size': '14px',
                                'font_weight': 'bold',
                                'shadow_color': 'rgba(0,0,0,0.5)',
                                'shadow_blur': '5px',
                                'shadow_offset': '2px'
                            },
                            'hover_effects': {
                                'scale': 1.1,
                                'brightness': 1.2,
                                'transition': 'all 0.3s ease'
                            },
                            'pulse_animation': {
                                'enabled': True,
                                'duration': '2s',
                                'scale_range': [0.95, 1.05]
                            }
                        }
                    })
                    
                except (ValueError, TypeError):
                    return create_error_response("Invalid coordinates", status.HTTP_400_BAD_REQUEST)
            else:
                # No location - return recent pins with a limit
                pins = queryset.order_by('-created_at')[:100]
                cluster_params = {
                    'enabled': True,
                    'distance': 60,
                    'max_cluster_radius': 100,
                    'visualization': {
                        'cluster_colors': {
                            'small': '#3388ff',
                            'medium': '#ff8833',
                            'large': '#ff3333'
                        },
                        'cluster_sizes': {
                            'small': {'min': 2, 'max': 10},
                            'medium': {'min': 11, 'max': 50},
                            'large': {'min': 51, 'max': None}
                        },
                        'cluster_styles': {
                            'border_color': '#ffffff',
                            'border_width': 2,
                            'text_color': '#ffffff',
                            'font_size': '14px',
                            'font_weight': 'bold',
                            'shadow_color': 'rgba(0,0,0,0.5)',
                            'shadow_blur': '5px',
                            'shadow_offset': '2px'
                        },
                        'hover_effects': {
                            'scale': 1.1,
                            'brightness': 1.2,
                            'transition': 'all 0.3s ease'
                        },
                        'pulse_animation': {
                            'enabled': True,
                            'duration': '2s',
                            'scale_range': [0.95, 1.05]
                        }
                    }
                }
                
            serializer = self.get_serializer(pins, many=True)
            response_data = serializer.data
            
            # Include cluster parameters in response
            return Response({
                'type': 'FeatureCollection',
                'features': response_data,
                'cluster_params': cluster_params
            })
            
        except Exception as e:
            logger.error(f"Error in list_map: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    @action(detail=False, methods=['get'])
    def nearby(self, request):
        """
        Get pins near a specific location with automatic seeding for sparse areas.

        This endpoint implements the music pin seeding system to solve the cold start
        problem by automatically generating seed pins when organic content is sparse.
        """
        try:
            lat = request.query_params.get('latitude')
            lng = request.query_params.get('longitude')
            radius = request.query_params.get('radius', 25000)  # Increased default to 25km to match seeding distances

            if not lat or not lng:
                return create_error_response("Latitude and longitude are required", status.HTTP_400_BAD_REQUEST)

            try:
                radius = int(float(radius))
                if radius > 25000:  # Increased limit to match seeding distances (25km)
                    radius = 25000
            except (ValueError, TypeError):
                radius = 1000

            try:
                lat_float = float(lat)
                lng_float = float(lng)
            except (ValueError, TypeError):
                return create_error_response("Invalid coordinates", status.HTTP_400_BAD_REQUEST)

            # Get organic pins first
            pins = get_nearby_pins(
                user=request.user,
                lat=lat_float,
                lng=lng_float,
                radius_meters=radius
            )

            # Convert to list for manipulation
            pins_list = list(pins)

            # Check for personalized seeding opportunities
            seeding_service = SeedingService()
            try:
                # Convert radius to kilometers for seeding logic
                search_radius_km = radius / 1000.0
                logger.info(f"[PERSONALIZED_SEEDING] 🔍 Checking seeding opportunities for {request.user.username} at ({lat_float}, {lng_float}) with {search_radius_km}km radius")

                # Check what type of seeding (if any) this user should receive
                seeding_eligibility = seeding_service.check_personalized_seeding_needed(
                    request.user, lat_float, lng_float, search_radius_km
                )

                if seeding_eligibility['should_seed']:
                    from django.core.cache import cache
                    import time

                    start_time = time.time()
                    seeding_type = seeding_eligibility['seeding_type']

                    logger.info(f"[PERSONALIZED_SEEDING] 🎯 Proceeding with {seeding_type} seeding for {request.user.username}")

                    # Create cache key that includes user and seeding type
                    cache_key = f"seed_pins:{lat_float:.3f}:{lng_float:.3f}:{request.user.id}:{seeding_type}"
                    cached_seeds = cache.get(cache_key)

                    if cached_seeds is not None:
                        seed_pins = cached_seeds
                        logger.info(f"[PERSONALIZED_SEEDING] 💾 Using cached {seeding_type} seed pins for {request.user.username} at ({lat_float}, {lng_float}) - {len(cached_seeds)} pins")
                    else:
                        seed_pins = []

                        if seeding_type == 'personalized':
                            # Generate personalized seeds for pre-seeded area
                            existing_area = seeding_eligibility.get('existing_area')
                            if existing_area:
                                logger.info(f"[PERSONALIZED_SEEDING] 🎵 Generating personalized seeds in existing area {existing_area.id}")
                                seed_pins = seeding_service.generate_personalized_seeds_for_area(
                                    request.user, lat_float, lng_float, existing_area
                                )
                                logger.info(f"[PERSONALIZED_SEEDING] ✅ Generated {len(seed_pins)} personalized seed pins for {request.user.username}")
                            else:
                                logger.warning(f"[PERSONALIZED_SEEDING] ⚠ No existing area provided for personalized seeding")

                        elif seeding_type == 'full':
                            # Generate full exploration seed pins
                            logger.info(f"[PERSONALIZED_SEEDING] 🌍 Generating full exploration seeds for new area")
                            seed_pins = seeding_service.generate_exploration_seed_pins(
                                lat_float, lng_float, user=request.user
                            )
                            logger.info(f"[PERSONALIZED_SEEDING] ✅ Generated {len(seed_pins)} full seed pins for {request.user.username}")

                        # Cache for 1 hour to avoid regenerating
                        if seed_pins:
                            cache.set(cache_key, seed_pins, 60 * 60)
                            logger.debug(f"[PERSONALIZED_SEEDING] 💾 Cached {len(seed_pins)} pins for future requests")

                    # Add seed pins to results
                    pins_list.extend(seed_pins)
                    logger.info(f"[PERSONALIZED_SEEDING] 📍 Added {len(seed_pins)} seed pins to response (total pins: {len(pins_list)})")

                    # Track seeding performance
                    seeding_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                    logger.info(f"[PERSONALIZED_SEEDING] ⏱ Personalized seeding evaluation completed in {seeding_time:.2f}ms")

                    # Ensure we don't exceed performance threshold (500ms)
                    if seeding_time > 500:
                        logger.warning(f"[PERSONALIZED_SEEDING] ⚠ Personalized seeding took {seeding_time:.2f}ms, exceeding 500ms threshold")

                else:
                    logger.info(f"[PERSONALIZED_SEEDING] ❌ No seeding needed for {request.user.username} at ({lat_float}, {lng_float}): {seeding_eligibility['reason']}")

            except Exception as seeding_error:
                # Graceful degradation - log error but continue with organic pins
                logger.error(f"[PERSONALIZED_SEEDING] ✗ Personalized seeding error (graceful degradation) for {request.user.username}: {str(seeding_error)}")
                # Continue with organic pins only

            # Mark pins as viewed and count fresh pins for notification
            fresh_pins_count = 0
            for pin in pins_list:
                # Check if user has viewed this pin before
                if not PinInteraction.objects.filter(
                    user=request.user,
                    pin=pin,
                    interaction_type='view'
                ).exists():
                    fresh_pins_count += 1

                # Record view interaction for all returned pins
                record_pin_interaction(
                    user=request.user,
                    pin=pin,
                    interaction_type='view'
                )

            # Send notification for fresh pins if there are enough
            if fresh_pins_count >= 2:
                notify_fresh_pins_discovered(
                    user=request.user,
                    pin_count=fresh_pins_count
                )

            # Sort pins by distance (organic pins first, then seeds)
            from django.contrib.gis.geos import Point
            user_location = Point(lng_float, lat_float, srid=4326)

            def get_distance(pin):
                if hasattr(pin, 'distance'):
                    return pin.distance.m
                else:
                    # Calculate distance for seed pins
                    from django.contrib.gis.measure import D
                    from django.contrib.gis.db.models.functions import Distance
                    return pin.location.distance(user_location) * 111000  # Rough conversion to meters

            pins_list.sort(key=get_distance)

            serializer = PinSerializer(pins_list, many=True, context={'request': request})
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error in nearby pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def nearby_fresh(self, request):
        """
        Get pins near a specific location that the user has not encountered (no interactions)
        """
        try:
            lat = request.query_params.get('latitude')
            lng = request.query_params.get('longitude')
            radius = request.query_params.get('radius', 25000)  # Increased default to 25km to match seeding distances
            
            if not lat or not lng:
                return create_error_response("Latitude and longitude are required", status.HTTP_400_BAD_REQUEST)
                
            try:
                radius = int(float(radius))
                if radius > 25000:  # Increased limit to match seeding distances (25km)
                    radius = 25000
            except (ValueError, TypeError):
                radius = 1000
                
            try:
                # Get nearby pins first (without limit)
                pins = get_nearby_pins(
                    user=request.user,
                    lat=float(lat),
                    lng=float(lng),
                    radius_meters=radius
                )
                
                # Filter out pins that the user has interacted with
                interacted_pin_ids = PinInteraction.objects.filter(
                    user=request.user,
                    interaction_type='collect'
                ).values_list('pin_id', flat=True)
                
                fresh_pins = pins.exclude(id__in=interacted_pin_ids)
                
            except (ValueError, TypeError):
                return create_error_response("Invalid coordinates", status.HTTP_400_BAD_REQUEST)
                
            # Mark fresh pins as viewed and send notification
            fresh_pins_count = fresh_pins.count()
            for pin in fresh_pins:
                # Record view interaction for fresh pins
                record_pin_interaction(
                    user=request.user,
                    pin=pin,
                    interaction_type='view'
                )
            
            # Send notification for fresh pins if there are enough
            if fresh_pins_count >= 2:
                notify_fresh_pins_discovered(
                    user=request.user,
                    pin_count=fresh_pins_count
                )
                
            serializer = PinSerializer(fresh_pins, many=True, context={'request': request})
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error in nearby fresh pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def nearby_friends(self, request):
        """
        Get pins near a specific location that are from the user's friends or themselves
        """
        try:
            lat = request.query_params.get('latitude')
            lng = request.query_params.get('longitude')
            radius = request.query_params.get('radius', 25000)  # Increased default to 25km to match seeding distances
            
            if not lat or not lng:
                return create_error_response("Latitude and longitude are required", status.HTTP_400_BAD_REQUEST)
                
            try:
                radius = int(float(radius))
                if radius > 25000:  # Increased limit to match seeding distances (25km)
                    radius = 25000
            except (ValueError, TypeError):
                radius = 1000
                
            try:
                # Get the user's friends
                
                # Get all friendships where user is either requester or recipient with accepted status
                friendships = Friend.objects.filter(
                    (Q(requester=request.user) | Q(recipient=request.user)),
                    status='accepted'
                )
                
                # Extract friend user IDs
                friend_ids = set()
                for friendship in friendships:
                    if friendship.requester == request.user:
                        friend_ids.add(friendship.recipient.id)
                    else:
                        friend_ids.add(friendship.requester.id)
                
                # Add the user's own ID to include their own pins
                friend_ids.add(request.user.id)
                
                # Get nearby pins first (without limit)
                pins = get_nearby_pins(
                    user=request.user,
                    lat=float(lat),
                    lng=float(lng),
                    radius_meters=radius
                )
                
                # Filter to only include pins from friends and self
                friends_pins = pins.filter(owner_id__in=friend_ids)
                
            except (ValueError, TypeError):
                return create_error_response("Invalid coordinates", status.HTTP_400_BAD_REQUEST)
                
            # Mark friends' pins as viewed and count fresh pins for notification
            fresh_pins_count = 0
            for pin in friends_pins:
                # Check if user has viewed this pin before
                if not PinInteraction.objects.filter(
                    user=request.user,
                    pin=pin,
                    interaction_type='view'
                ).exists():
                    fresh_pins_count += 1
                
                # Record view interaction for all returned pins
                record_pin_interaction(
                    user=request.user,
                    pin=pin,
                    interaction_type='view'
                )
            
            # Send notification for fresh pins if there are enough
            if fresh_pins_count >= 2:
                notify_fresh_pins_discovered(
                    user=request.user,
                    pin_count=fresh_pins_count
                )
                
            serializer = PinSerializer(friends_pins, many=True, context={'request': request})
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error in nearby friends pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def trending(self, request):
        """
        Get trending pins based on interaction count
        """
        try:
            days = request.query_params.get('days', 7)
            limit = request.query_params.get('limit', 20)
            
            try:
                days = int(days)
                limit = int(limit)
                if limit > 100:  # Limit maximum results
                    limit = 100
            except (ValueError, TypeError):
                days = 7
                limit = 20
                
            pins = get_trending_pins(days=days, limit=limit)
            serializer = PinSerializer(pins, many=True)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error in trending pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def my_pins(self, request):
        """
        Get only the current user's pins (both public and private)
        ENHANCED: Now includes engagement counts (likes, comments, views) and flexible sorting
        
        Query Parameters:
        - ordering: recent (default), most_liked, most_engaged, most_comments, most_views, most_collects, most_shares, oldest
        - is_private: true/false to filter by privacy status
        - page, page_size: for pagination
        """
        try:
            user = request.user
            
            # Get only the current user's pins with engagement counts
            from votes.models import Vote
            from comments.models import Comment
            
            queryset = Pin.objects.filter(
                owner=user
            ).filter(
                models.Q(expiration_date__isnull=True) | 
                models.Q(expiration_date__gt=timezone.now())
            ).select_related('owner', 'skin').annotate(
                # ✅ Add engagement counts using database annotations for performance
                likes_count=Count('votes', filter=models.Q(votes__value=1)),  # Upvotes = Likes
                dislikes_count=Count('votes', filter=models.Q(votes__value=-1)),  # Downvotes = Dislikes
                comments_count=Count('comments', filter=models.Q(comments__is_hidden=False)),  # Comments (not hidden)
                views_count=Count('interactions', filter=models.Q(interactions__interaction_type='view')),  # View interactions
                collects_count=Count('interactions', filter=models.Q(interactions__interaction_type='collect')),  # Collect interactions
                shares_count=Count('interactions', filter=models.Q(interactions__interaction_type='share')),  # Share interactions
            )
            
            # Apply privacy filter if provided
            is_private = request.query_params.get('is_private')
            if is_private is not None:
                if is_private.lower() == 'true':
                    queryset = queryset.filter(is_private=True)
                elif is_private.lower() == 'false':
                    queryset = queryset.filter(is_private=False)
            
            # ✅ ENHANCED: Apply sorting based on ordering parameter
            ordering = request.query_params.get('ordering', 'recent').lower()
            
            # Map ordering options to database fields
            ordering_map = {
                'recent': '-created_at',                    # Most recent first (default)
                'oldest': 'created_at',                     # Oldest first
                'most_liked': '-likes_count',               # Most liked first
                'most_comments': '-comments_count',         # Most comments first
                'most_views': '-views_count',               # Most views first
                'most_collects': '-collects_count',         # Most collects first
                'most_shares': '-shares_count',             # Most shares first
                'least_liked': 'likes_count',               # Least liked first
            }
            
            # Apply ordering (most_engaged handled separately)
            if ordering == 'most_engaged' or ordering == 'least_engaged':
                # For engagement sorting, we'll sort in Python after getting the data
                queryset = queryset.order_by('-created_at')  # Temp order
            elif ordering in ordering_map:
                queryset = queryset.order_by(ordering_map[ordering], '-created_at')  # Secondary sort by recent
            else:
                queryset = queryset.order_by('-created_at')  # Default fallback
                logger.warning(f"Invalid ordering parameter '{ordering}' provided to my_pins. Using default 'recent'.")
            
            # Get all available ordering options for frontend
            all_ordering_options = list(ordering_map.keys()) + ['most_engaged', 'least_engaged']
            
            # Add pagination support
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = PinSerializer(page, many=True, context={'request': request})
                # ✅ Add engagement counts to each pin in the response
                response_data = serializer.data
                for i, pin_data in enumerate(response_data):
                    pin_obj = page[i]
                    total_engagement = (
                        getattr(pin_obj, 'likes_count', 0) + 
                        getattr(pin_obj, 'dislikes_count', 0) + 
                        getattr(pin_obj, 'comments_count', 0) + 
                        getattr(pin_obj, 'views_count', 0) + 
                        getattr(pin_obj, 'collects_count', 0) + 
                        getattr(pin_obj, 'shares_count', 0)
                    )
                    pin_data['engagement_counts'] = {
                        'likes': getattr(pin_obj, 'likes_count', 0),
                        'dislikes': getattr(pin_obj, 'dislikes_count', 0),
                        'comments': getattr(pin_obj, 'comments_count', 0),
                        'views': getattr(pin_obj, 'views_count', 0),
                        'collects': getattr(pin_obj, 'collects_count', 0),
                        'shares': getattr(pin_obj, 'shares_count', 0),
                        'total_engagement': total_engagement
                    }
                    # Store total engagement for sorting
                    pin_data['_total_engagement_sort'] = total_engagement
                
                # ✅ Apply engagement-based sorting if requested
                if ordering == 'most_engaged':
                    response_data.sort(key=lambda x: x['_total_engagement_sort'], reverse=True)
                elif ordering == 'least_engaged':
                    response_data.sort(key=lambda x: x['_total_engagement_sort'], reverse=False)
                
                # Clean up temporary sorting field
                for pin_data in response_data:
                    pin_data.pop('_total_engagement_sort', None)
                
                # ✅ Add sorting metadata to response
                paginated_response = self.get_paginated_response(response_data)
                paginated_response.data['sorting'] = {
                    'current_ordering': ordering,
                    'available_options': all_ordering_options,
                    'applied_sort': ordering_map.get(ordering, f'python:{ordering}' if ordering in ['most_engaged', 'least_engaged'] else '-created_at')
                }
                return paginated_response
            
            # Non-paginated response
            serializer = PinSerializer(queryset, many=True, context={'request': request})
            response_data = serializer.data
            for i, pin_data in enumerate(response_data):
                pin_obj = queryset[i]
                total_engagement = (
                    getattr(pin_obj, 'likes_count', 0) + 
                    getattr(pin_obj, 'dislikes_count', 0) + 
                    getattr(pin_obj, 'comments_count', 0) + 
                    getattr(pin_obj, 'views_count', 0) + 
                    getattr(pin_obj, 'collects_count', 0) + 
                    getattr(pin_obj, 'shares_count', 0)
                )
                pin_data['engagement_counts'] = {
                    'likes': getattr(pin_obj, 'likes_count', 0),
                    'dislikes': getattr(pin_obj, 'dislikes_count', 0),
                    'comments': getattr(pin_obj, 'comments_count', 0),
                    'views': getattr(pin_obj, 'views_count', 0),
                    'collects': getattr(pin_obj, 'collects_count', 0),
                    'shares': getattr(pin_obj, 'shares_count', 0),
                    'total_engagement': total_engagement
                }
                # Store total engagement for sorting
                pin_data['_total_engagement_sort'] = total_engagement
            
            # ✅ Apply engagement-based sorting if requested
            if ordering == 'most_engaged':
                response_data.sort(key=lambda x: x['_total_engagement_sort'], reverse=True)
            elif ordering == 'least_engaged':
                response_data.sort(key=lambda x: x['_total_engagement_sort'], reverse=False)
            
            # Clean up temporary sorting field
            for pin_data in response_data:
                pin_data.pop('_total_engagement_sort', None)
            
            # ✅ Add sorting metadata to non-paginated response
            return Response({
                'results': response_data,
                'sorting': {
                    'current_ordering': ordering,
                    'available_options': all_ordering_options,
                    'applied_sort': ordering_map.get(ordering, f'python:{ordering}' if ordering in ['most_engaged', 'least_engaged'] else '-created_at')
                }
            })
            
        except Exception as e:
            logger.error(f"Error retrieving user's pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def collected(self, request):
        """
        Get pins that the current user has collected (paginated)
        
        Query Parameters:
        - ordering: collected_recent (default), collected_oldest, pin_recent, pin_oldest, most_liked, most_comments, most_views, most_shares
        - page, page_size: for pagination
        """
        try:
            user = request.user
            
            # Get pins that the user has collected through PinInteraction
            collected_pin_ids = PinInteraction.objects.filter(
                user=user,
                interaction_type='collect'
            ).values_list('pin_id', flat=True)
            
            # Get the actual pins with engagement counts
            from votes.models import Vote
            from comments.models import Comment
            
            queryset = Pin.objects.filter(
                id__in=collected_pin_ids
            ).filter(
                models.Q(expiration_date__isnull=True) | 
                models.Q(expiration_date__gt=timezone.now())
            ).select_related('owner', 'skin').annotate(
                # Add engagement counts using database annotations for performance
                likes_count=Count('votes', filter=models.Q(votes__value=1)),  # Upvotes = Likes
                dislikes_count=Count('votes', filter=models.Q(votes__value=-1)),  # Downvotes = Dislikes
                comments_count=Count('comments', filter=models.Q(comments__is_hidden=False)),  # Comments (not hidden)
                views_count=Count('interactions', filter=models.Q(interactions__interaction_type='view')),  # View interactions
                collects_count=Count('interactions', filter=models.Q(interactions__interaction_type='collect')),  # Collect interactions
                shares_count=Count('interactions', filter=models.Q(interactions__interaction_type='share')),  # Share interactions
            )
            
            # Get collection timestamps for sorting
            collection_timestamps = {}
            for interaction in PinInteraction.objects.filter(
                user=user,
                interaction_type='collect',
                pin_id__in=collected_pin_ids
            ).values('pin_id', 'created_at'):
                collection_timestamps[interaction['pin_id']] = interaction['created_at']
            
            # Apply sorting based on ordering parameter
            ordering = request.query_params.get('ordering', 'collected_recent').lower()
            
            # Map ordering options to database fields
            ordering_map = {
                'pin_recent': '-created_at',                    # Pin creation date, most recent first
                'pin_oldest': 'created_at',                     # Pin creation date, oldest first
                'most_liked': '-likes_count',                   # Most liked first
                'most_comments': '-comments_count',             # Most comments first
                'most_views': '-views_count',                   # Most views first
                'most_shares': '-shares_count',                 # Most shares first
                'least_liked': 'likes_count',                   # Least liked first
            }
            
            # Apply database-based ordering
            if ordering in ordering_map:
                queryset = queryset.order_by(ordering_map[ordering], '-created_at')  # Secondary sort by recent
            else:
                # Default to collected_recent or collected_oldest (handled in Python)
                queryset = queryset.order_by('-created_at')  # Temp order
            
            # Get all available ordering options for frontend
            all_ordering_options = list(ordering_map.keys()) + ['collected_recent', 'collected_oldest']
            
            # Add pagination support
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = PinSerializer(page, many=True, context={'request': request})
                response_data = serializer.data
                
                # Add collection timestamp and engagement counts to each pin
                for i, pin_data in enumerate(response_data):
                    pin_obj = page[i]
                    pin_id = pin_obj.id
                    
                    # Add collection timestamp
                    pin_data['collected_at'] = collection_timestamps.get(pin_id)
                    
                    # Add engagement counts
                    total_engagement = (
                        getattr(pin_obj, 'likes_count', 0) + 
                        getattr(pin_obj, 'dislikes_count', 0) + 
                        getattr(pin_obj, 'comments_count', 0) + 
                        getattr(pin_obj, 'views_count', 0) + 
                        getattr(pin_obj, 'collects_count', 0) + 
                        getattr(pin_obj, 'shares_count', 0)
                    )
                    pin_data['engagement_counts'] = {
                        'likes': getattr(pin_obj, 'likes_count', 0),
                        'dislikes': getattr(pin_obj, 'dislikes_count', 0),
                        'comments': getattr(pin_obj, 'comments_count', 0),
                        'views': getattr(pin_obj, 'views_count', 0),
                        'collects': getattr(pin_obj, 'collects_count', 0),
                        'shares': getattr(pin_obj, 'shares_count', 0),
                        'total_engagement': total_engagement
                    }
                    # Store collection timestamp for sorting
                    pin_data['_collected_at_sort'] = collection_timestamps.get(pin_id)
                
                # Apply collection-based sorting if requested
                if ordering == 'collected_recent':
                    response_data.sort(key=lambda x: x['_collected_at_sort'], reverse=True)
                elif ordering == 'collected_oldest':
                    response_data.sort(key=lambda x: x['_collected_at_sort'], reverse=False)
                
                # Clean up temporary sorting field
                for pin_data in response_data:
                    pin_data.pop('_collected_at_sort', None)
                
                # Add sorting metadata to response
                paginated_response = self.get_paginated_response(response_data)
                paginated_response.data['sorting'] = {
                    'current_ordering': ordering,
                    'available_options': all_ordering_options,
                    'applied_sort': ordering_map.get(ordering, f'python:{ordering}' if ordering in ['collected_recent', 'collected_oldest'] else '-created_at')
                }
                return paginated_response
            
            # Non-paginated response
            serializer = PinSerializer(queryset, many=True, context={'request': request})
            response_data = serializer.data
            
            # Add collection timestamp and engagement counts to each pin
            for i, pin_data in enumerate(response_data):
                pin_obj = queryset[i]
                pin_id = pin_obj.id
                
                # Add collection timestamp
                pin_data['collected_at'] = collection_timestamps.get(pin_id)
                
                # Add engagement counts
                total_engagement = (
                    getattr(pin_obj, 'likes_count', 0) + 
                    getattr(pin_obj, 'dislikes_count', 0) + 
                    getattr(pin_obj, 'comments_count', 0) + 
                    getattr(pin_obj, 'views_count', 0) + 
                    getattr(pin_obj, 'collects_count', 0) + 
                    getattr(pin_obj, 'shares_count', 0)
                )
                pin_data['engagement_counts'] = {
                    'likes': getattr(pin_obj, 'likes_count', 0),
                    'dislikes': getattr(pin_obj, 'dislikes_count', 0),
                    'comments': getattr(pin_obj, 'comments_count', 0),
                    'views': getattr(pin_obj, 'views_count', 0),
                    'collects': getattr(pin_obj, 'collects_count', 0),
                    'shares': getattr(pin_obj, 'shares_count', 0),
                    'total_engagement': total_engagement
                }
                # Store collection timestamp for sorting
                pin_data['_collected_at_sort'] = collection_timestamps.get(pin_id)
            
            # Apply collection-based sorting if requested
            if ordering == 'collected_recent':
                response_data.sort(key=lambda x: x['_collected_at_sort'], reverse=True)
            elif ordering == 'collected_oldest':
                response_data.sort(key=lambda x: x['_collected_at_sort'], reverse=False)
            
            # Clean up temporary sorting field
            for pin_data in response_data:
                pin_data.pop('_collected_at_sort', None)
            
            # Add sorting metadata to non-paginated response
            return Response({
                'results': response_data,
                'sorting': {
                    'current_ordering': ordering,
                    'available_options': all_ordering_options,
                    'applied_sort': ordering_map.get(ordering, f'python:{ordering}' if ordering in ['collected_recent', 'collected_oldest'] else '-created_at')
                }
            })
            
        except Exception as e:
            logger.error(f"Error retrieving user's collected pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def view(self, request, pk=None):
        """
        Record a view interaction with a pin
        """
        return self._record_interaction(request, pk, 'view')
    
    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """
        Record a like interaction with a pin
        """
        return self._record_interaction(request, pk, 'like')
    
    @action(detail=True, methods=['post'])
    def collect(self, request, pk=None):
        """
        Record a collect interaction with a pin
        """
        return self._record_interaction(request, pk, 'collect')
    
    @action(detail=True, methods=['post'])
    def share(self, request, pk=None):
        """
        Record a share interaction with a pin
        """
        return self._record_interaction(request, pk, 'share')
    
    @action(detail=True, methods=['get'])
    def map_details(self, request, pk=None):
        """
        Get detailed pin information for map display with aura visualization settings
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Record view interaction if not already viewed in the last hour
            if not PinInteraction.objects.filter(
                user=request.user, 
                pin=pin, 
                interaction_type='view',
                created_at__gte=timezone.now() - timedelta(hours=1)
            ).exists():
                record_pin_interaction(
                    user=request.user,
                    pin=pin,
                    interaction_type='view'
                )
            
            # Get details with customized serializer for map display
            serializer = PinSerializer(pin)
            data = serializer.data
            
            # Define color mapping based on music service and rarity
            service_colors = {
                'spotify': '#1DB954',
                'apple': '#FC3C44',
                'soundcloud': '#FF7700'
            }
            
            # Add visualization settings based on pin properties
            icon_url = None
            if hasattr(pin, 'skin') and pin.skin:
                icon_url = pin.skin.image_url if hasattr(pin.skin, 'image_url') else None
                
            data['visualization'] = {
                'aura_color': service_colors.get(pin.service, '#3388ff'),
                'aura_opacity': 0.7,  # Default opacity for all pins
                'pulse_animation': pin.created_at > (timezone.now() - timedelta(hours=24)),
                'icon_url': icon_url
            }
            
            return Response(data)
            
        except Exception as e:
            logger.error(f"Error getting pin map details: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def vote_info(self, request, pk=None):
        """
        Get comprehensive voting information for a pin in a single optimized call.
        Returns vote counts, user's current vote, and pin basic info.
        Ultra-fast endpoint optimized for frontend voting widgets.
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Get user's current vote (if any) - single optimized query
            from votes.models import Vote
            user_vote = Vote.objects.filter(user=request.user, pin=pin).first()
            user_vote_value = user_vote.value if user_vote else None
            
            # Get voting statistics - using cached counts from pin model for speed
            vote_data = {
                'pin_id': pin.id,
                'total_votes': pin.upvote_count + pin.downvote_count,
                'upvotes': pin.upvote_count,
                'downvotes': pin.downvote_count,
                'score': pin.vote_score,
                'upvote_ratio': pin.upvote_count / max(pin.upvote_count + pin.downvote_count, 1),
                'user_vote': user_vote_value,  # null, 1, or -1
                'can_vote': pin.owner != request.user,  # Can't vote on own pins
                
                # Additional context for frontend
                'pin_info': {
                    'title': pin.title,
                    'track_title': pin.track_title,
                    'track_artist': pin.track_artist,
                    'owner': {
                        'id': pin.owner.id,
                        'username': pin.owner.username,
                        'profile_pic': request.build_absolute_uri(pin.owner.profile_pic) if pin.owner.profile_pic else None
                    },
                    'created_at': pin.created_at,
                    'service': pin.service
                }
            }
            
            return Response(vote_data)
            
        except Exception as e:
            logger.error(f"Error getting vote info for pin {pk}: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def vote_post(self, request, pk=None):
        """
        Cast a vote on a pin in a single optimized call.
        Handles upvoting, downvoting, and vote removal.
        Ultra-fast endpoint optimized for frontend voting widgets.
        
        Request body:
        {
            "value": 1    // 1 for upvote, -1 for downvote, 0 or null to remove vote
        }
        
        Returns updated vote information immediately.
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Get vote value from request
            vote_value = request.data.get('value')
            
            # Validate vote value
            if vote_value is not None and vote_value not in [1, -1, 0]:
                return create_error_response("Vote value must be 1 (upvote), -1 (downvote), or 0 (remove)", status.HTTP_400_BAD_REQUEST)
            
            # Handle voting logic with atomic transaction for consistency
            from votes.models import Vote
            with transaction.atomic():
                # Get existing vote
                existing_vote = Vote.objects.filter(user=request.user, pin=pin).first()
                
                if vote_value is None or vote_value == 0:
                    # Remove vote if it exists
                    if existing_vote:
                        existing_vote.delete()
                        logger.info(f"Vote removed: {request.user.username} removed vote from pin {pin.id}")
                    current_vote = None
                    
                elif existing_vote:
                    # Update existing vote
                    if existing_vote.value != vote_value:
                        existing_vote.value = vote_value
                        existing_vote.save()
                        logger.info(f"Vote updated: {request.user.username} changed vote to {vote_value} on pin {pin.id}")
                    current_vote = vote_value
                    
                else:
                    # Create new vote
                    Vote.objects.create(user=request.user, pin=pin, value=vote_value)
                    logger.info(f"Vote created: {request.user.username} voted {vote_value} on pin {pin.id}")
                    current_vote = vote_value
                
                # Refresh pin from database to get updated vote counts
                pin.refresh_from_db()
            
            # Return updated vote information (same format as vote_info for consistency)
            vote_data = {
                'pin_id': pin.id,
                'total_votes': pin.upvote_count + pin.downvote_count,
                'upvotes': pin.upvote_count,
                'downvotes': pin.downvote_count,
                'score': pin.vote_score,
                'upvote_ratio': pin.upvote_count / max(pin.upvote_count + pin.downvote_count, 1),
                'user_vote': current_vote,  # null, 1, or -1
                'can_vote': True,  # Always true - users can vote on any visible pin including their own
                'action': 'vote_updated',
                
                # Include basic pin info for convenience
                'pin_info': {
                    'title': pin.title,
                    'track_title': pin.track_title,
                    'track_artist': pin.track_artist,
                    'owner': {
                        'id': pin.owner.id,
                        'username': pin.owner.username,
                        'profile_pic': request.build_absolute_uri(pin.owner.profile_pic) if pin.owner.profile_pic else None
                    },
                    'created_at': pin.created_at,
                    'service': pin.service
                }
            }
            
            return Response(vote_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error posting vote for pin {pk}: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def comment_info(self, request, pk=None):
        """
        Get comprehensive comment information for a pin in a single optimized call.
        Returns all comments with user profiles and metadata.
        Ultra-fast endpoint optimized for frontend comment widgets.
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Get comments with user data - optimized query with select_related
            from comments.models import Comment
            comments_query = Comment.objects.filter(
                pin=pin,
                is_hidden=False
            ).select_related('user').order_by('-created_at')
            
            # Apply pagination parameters
            limit = min(int(request.query_params.get('limit', 20)), 50)  # Max 50 comments
            offset = int(request.query_params.get('offset', 0))
            
            total_count = comments_query.count()
            comments = comments_query[offset:offset + limit]
            
            # Format comments with user profiles
            comments_data = []
            for comment in comments:
                comment_data = {
                    'id': comment.id,
                    'text': comment.text,
                    'created_at': comment.created_at,
                    'updated_at': comment.updated_at,
                    'is_edited': comment.is_edited,
                    'user': {
                        'id': comment.user.id,
                        'username': comment.user.username,
                        'profile_pic': request.build_absolute_uri(comment.user.profile_pic) if comment.user.profile_pic else None
                    },
                    'is_owner': comment.user == request.user  # Can edit/delete own comments
                }
                comments_data.append(comment_data)
            
            # Prepare response data
            comment_data = {
                'pin_id': pin.id,
                'comment_count': total_count,
                'comments': comments_data,
                'pagination': {
                    'limit': limit,
                    'offset': offset,
                    'has_more': (offset + limit) < total_count,
                    'total': total_count
                },
                'can_comment': True,  # All authenticated users can comment
                
                # Include basic pin info for context
                'pin_info': {
                    'title': pin.title,
                    'track_title': pin.track_title,
                    'track_artist': pin.track_artist,
                    'owner': {
                        'id': pin.owner.id,
                        'username': pin.owner.username,
                        'profile_pic': request.build_absolute_uri(pin.owner.profile_pic) if pin.owner.profile_pic else None
                    },
                    'created_at': pin.created_at,
                    'service': pin.service
                }
            }
            
            return Response(comment_data)
            
        except Exception as e:
            logger.error(f"Error getting comment info for pin {pk}: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def comment_post(self, request, pk=None):
        """
        Add a comment to a pin in a single optimized call.
        Returns the new comment and updated comment list.
        Ultra-fast endpoint optimized for frontend comment widgets.
        
        Request body:
        {
            "text": "Great track! Love this drop 🔥"
        }
        
        Returns the new comment and updated count immediately.
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Get comment text from request
            comment_text = request.data.get('text', '').strip()
            
            # Validate comment text
            if not comment_text:
                return create_error_response("Comment text cannot be empty", status.HTTP_400_BAD_REQUEST)
            
            if len(comment_text) > 1000:  # Same limit as Comment model
                return create_error_response("Comment text is too long (max 1000 characters)", status.HTTP_400_BAD_REQUEST)
            
            # Create comment with atomic transaction
            from comments.models import Comment
            with transaction.atomic():
                # Create the new comment
                new_comment = Comment.objects.create(
                    user=request.user,
                    pin=pin,
                    text=comment_text
                )
                
                # Get updated comment count
                total_comments = Comment.objects.filter(pin=pin, is_hidden=False).count()
                
                logger.info(f"Comment created: {request.user.username} commented on pin {pin.id}")
            
            # Format the new comment data
            new_comment_data = {
                'id': new_comment.id,
                'text': new_comment.text,
                'created_at': new_comment.created_at,
                'updated_at': new_comment.updated_at,
                'is_edited': new_comment.is_edited,
                'user': {
                    'id': new_comment.user.id,
                    'username': new_comment.user.username,
                    'profile_pic': request.build_absolute_uri(new_comment.user.profile_pic) if new_comment.user.profile_pic else None
                },
                'is_owner': True  # User just created this comment
            }
            
            # Return response with new comment and updated info
            response_data = {
                'pin_id': pin.id,
                'comment_id': new_comment.id,
                'comment_count': total_comments,
                'new_comment': new_comment_data,
                'action': 'comment_added',
                'can_comment': True,
                
                # Include basic pin info for context
                'pin_info': {
                    'title': pin.title,
                    'track_title': pin.track_title,
                    'track_artist': pin.track_artist,
                    'owner': {
                        'id': pin.owner.id,
                        'username': pin.owner.username,
                        'profile_pic': request.build_absolute_uri(pin.owner.profile_pic) if pin.owner.profile_pic else None
                    },
                    'created_at': pin.created_at,
                    'service': pin.service
                }
            }
            
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error posting comment for pin {pk}: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _record_interaction(self, request, pk, interaction_type):
        """
        Helper method to record pin interactions
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Special handling for test environment
            # In tests, the permission checks are handled within the test itself
            if settings.TESTING:
                # Record the interaction without further permission checks for tests
                interaction = record_pin_interaction(
                    user=request.user,
                    pin=pin,
                    interaction_type=interaction_type
                )
                
                # For collect interaction, increment the user's pins_collected count
                if interaction_type == 'collect':
                    with transaction.atomic():
                        request.user.increment_pins_collected()
                
                return Response({
                    "success": True,
                    "message": f"Pin {interaction_type} recorded successfully"
                })
                
            # Record the interaction
            interaction = record_pin_interaction(
                user=request.user,
                pin=pin,
                interaction_type=interaction_type
            )
            
            # For collect interaction, increment the user's pins_collected count
            if interaction_type == 'collect':
                with transaction.atomic():
                    request.user.increment_pins_collected()
            
            # Trigger notifications for relevant interactions
            if interaction_type == 'like':
                # Get current like count for the pin
                like_count = PinInteraction.objects.filter(
                    pin=pin,
                    interaction_type='like'
                ).count()
                
                # Send notification to pin owner
                notify_pin_liked(pin, request.user, like_count)
                
                # Check if pin should be marked as trending
                if like_count >= 5:  # Threshold for trending
                    view_count = PinInteraction.objects.filter(
                        pin=pin,
                        interaction_type='view'
                    ).count()
                    if view_count >= 20:  # Additional threshold
                        notify_pin_trending(pin, view_count)
                    
            return Response({
                "success": True,
                "message": f"Pin {interaction_type} recorded successfully"
            })
            
        except Exception as e:
            logger.error(f"Error recording pin {interaction_type}: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def interactions(self, request, pk=None):
        """
        Get all interactions for a specific pin (users who liked, viewed, etc.)
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Get interaction type filter
            interaction_type = request.query_params.get('type', 'all')
            
            # Base queryset
            interactions_query = PinInteraction.objects.filter(pin=pin).select_related('user')
            
            # Filter by interaction type if specified
            if interaction_type != 'all':
                if interaction_type not in ['view', 'like', 'collect', 'share']:
                    return create_error_response("Invalid interaction type", status.HTTP_400_BAD_REQUEST)
                interactions_query = interactions_query.filter(interaction_type=interaction_type)
            
            # Order by most recent first
            interactions = interactions_query.order_by('-created_at')
            
            # Paginate results
            page = self.paginate_queryset(interactions)
            if page is not None:
                # Format the data to include user info
                data = []
                for interaction in page:
                    user_data = {
                        'id': interaction.user.id,
                        'username': interaction.user.username,
                        'profile_pic': request.build_absolute_uri(interaction.user.profile_pic) if interaction.user.profile_pic else None,
                    }
                    
                    interaction_data = {
                        'id': interaction.id,
                        'user': user_data,
                        'interaction_type': interaction.interaction_type,
                        'created_at': interaction.created_at
                    }
                    data.append(interaction_data)
                
                return self.get_paginated_response(data)
            
            # Non-paginated response
            data = []
            for interaction in interactions:
                user_data = {
                    'id': interaction.user.id,
                    'username': interaction.user.username,
                    'profile_pic': request.build_absolute_uri(interaction.user.profile_pic) if interaction.user.profile_pic else None,
                }
                
                interaction_data = {
                    'id': interaction.id,
                    'user': user_data,
                    'interaction_type': interaction.interaction_type,
                    'created_at': interaction.created_at
                }
                data.append(interaction_data)
            
            return Response(data)
            
        except Exception as e:
            logger.error(f"Error getting pin interactions: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def likes(self, request, pk=None):
        """
        Get users who liked a specific pin
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Get like interactions
            likes = PinInteraction.objects.filter(
                pin=pin,
                interaction_type='like'
            ).select_related('user').order_by('-created_at')
            
            # Paginate results
            page = self.paginate_queryset(likes)
            if page is not None:
                data = []
                for like in page:
                    user_data = {
                        'id': like.user.id,
                        'username': like.user.username,
                        'profile_pic': request.build_absolute_uri(like.user.profile_pic) if like.user.profile_pic else None,
                        'liked_at': like.created_at
                    }
                    data.append(user_data)
                
                return self.get_paginated_response(data)
            
            # Non-paginated response
            data = []
            for like in likes:
                user_data = {
                    'id': like.user.id,
                    'username': like.user.username,
                    'profile_pic': request.build_absolute_uri(like.user.profile_pic) if like.user.profile_pic else None,
                    'liked_at': like.created_at
                }
                data.append(user_data)
            
            return Response(data)
            
        except Exception as e:
            logger.error(f"Error getting pin likes: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def views(self, request, pk=None):
        """
        Get users who viewed/played a specific pin
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Only pin owner can see who viewed their pins (privacy)
            if pin.owner != request.user:
                return create_error_response("Only pin owner can see view details", status.HTTP_403_FORBIDDEN)
            
            # Get view interactions
            views = PinInteraction.objects.filter(
                pin=pin,
                interaction_type='view'
            ).select_related('user').order_by('-created_at')
            
            # Paginate results
            page = self.paginate_queryset(views)
            if page is not None:
                data = []
                for view in page:
                    user_data = {
                        'id': view.user.id,
                        'username': view.user.username,
                        'profile_pic': request.build_absolute_uri(view.user.profile_pic) if view.user.profile_pic else None,
                        'viewed_at': view.created_at
                    }
                    data.append(user_data)
                
                return self.get_paginated_response(data)
            
            # Non-paginated response
            data = []
            for view in views:
                user_data = {
                    'id': view.user.id,
                    'username': view.user.username,
                    'profile_pic': request.build_absolute_uri(view.user.profile_pic) if view.user.profile_pic else None,
                    'viewed_at': view.created_at
                }
                data.append(user_data)
            
            return Response(data)
            
        except Exception as e:
            logger.error(f"Error getting pin views: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def engagement(self, request, pk=None):
        """
        Get comprehensive engagement data for a pin (likes=upvotes, views, comments, shares)
        ENHANCED: Now includes detailed user lists like bop drops engagement
        """
        try:
            pin = self.get_object()
            
            # Check if the pin is visible to the user
            if not check_pin_visibility(pin, request.user):
                return create_error_response("Pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Get REAL-TIME voting counts (upvotes = likes, downvotes = dislikes)
            from votes.models import Vote
            vote_stats = Vote.objects.filter(pin=pin).aggregate(
                upvotes=Count('id', filter=models.Q(value=1)),
                downvotes=Count('id', filter=models.Q(value=-1)),
                total_votes=Count('id')
            )
            
            # Get interaction counts (views, collects, shares)
            interaction_counts = PinInteraction.objects.filter(pin=pin).values('interaction_type').annotate(count=Count('id'))
            
            # Format interaction counts
            interaction_data = {
                'views': 0,
                'collects': 0, 
                'shares': 0
            }
            
            for item in interaction_counts:
                if item['interaction_type'] in interaction_data:
                    interaction_data[item['interaction_type']] = item['count']
            
            # Get comment count (real-time)
            from comments.models import Comment
            comment_count = Comment.objects.filter(pin=pin, is_hidden=False).count()
            
            # Build comprehensive counts (using upvotes as likes)
            counts = {
                'likes': vote_stats.get('upvotes', 0),  # ✅ FIXED: Upvotes = Likes  
                'dislikes': vote_stats.get('downvotes', 0),  # ✅ NEW: Show downvotes too
                'views': interaction_data['views'],
                'collects': interaction_data['collects'],
                'shares': interaction_data['shares'],
                'comments': comment_count
            }
            
            # ✅ NEW: Get detailed user lists (like bop drops engagement)
            
            # Get users who upvoted (likes)
            upvotes_list = []
            upvotes = Vote.objects.filter(pin=pin, value=1).select_related('user').order_by('-created_at')
            for vote in upvotes:
                profile_pic_url = None
                if vote.user.profile_pic:
                    profile_pic_url = request.build_absolute_uri(vote.user.profile_pic)
                    
                upvotes_list.append({
                    'user_id': vote.user.id,
                    'username': vote.user.username,
                    'first_name': vote.user.first_name,
                    'last_name': vote.user.last_name,
                    'profile_pic': profile_pic_url,
                    'voted_at': vote.created_at
                })
            
            # Get users who downvoted (dislikes) 
            downvotes_list = []
            downvotes = Vote.objects.filter(pin=pin, value=-1).select_related('user').order_by('-created_at')
            for vote in downvotes:
                profile_pic_url = None
                if vote.user.profile_pic:
                    profile_pic_url = request.build_absolute_uri(vote.user.profile_pic)
                    
                downvotes_list.append({
                    'user_id': vote.user.id,
                    'username': vote.user.username,
                    'first_name': vote.user.first_name,
                    'last_name': vote.user.last_name,
                    'profile_pic': profile_pic_url,
                    'voted_at': vote.created_at
                })
            
            # Get users who viewed (only if user is pin owner for privacy)
            views_list = []
            if pin.owner == request.user:
                views = PinInteraction.objects.filter(pin=pin, interaction_type='view').select_related('user').order_by('-created_at')
                for view in views:
                    profile_pic_url = None
                    if view.user.profile_pic:
                        profile_pic_url = request.build_absolute_uri(view.user.profile_pic)
                        
                    views_list.append({
                        'user_id': view.user.id,
                        'username': view.user.username,
                        'first_name': view.user.first_name,
                        'last_name': view.user.last_name,
                        'profile_pic': profile_pic_url,
                        'viewed_at': view.created_at
                    })
            
            # Get comments with content and user details
            comments_list = []
            comments = Comment.objects.filter(pin=pin, is_hidden=False).select_related('user').order_by('-created_at')
            for comment in comments:
                profile_pic_url = None
                if comment.user.profile_pic:
                    profile_pic_url = request.build_absolute_uri(comment.user.profile_pic)
                    
                comments_list.append({
                    'comment_id': comment.id,
                    'text': comment.text,
                    'user_id': comment.user.id,
                    'username': comment.user.username,
                    'first_name': comment.user.first_name,
                    'last_name': comment.user.last_name,
                    'profile_pic': profile_pic_url,
                    'commented_at': comment.created_at,
                    'is_edited': comment.is_edited
                })
            
            # Get recent activity (voting + interactions + comments)
            recent_activity = []
            
            # Recent votes (last 5)
            recent_votes = Vote.objects.filter(pin=pin).select_related('user').order_by('-created_at')[:5]
            for vote in recent_votes:
                profile_pic_url = None
                if vote.user.profile_pic:
                    profile_pic_url = request.build_absolute_uri(vote.user.profile_pic)
                    
                recent_activity.append({
                    'user': {
                        'id': vote.user.id,
                        'username': vote.user.username,
                        'profile_pic': profile_pic_url,
                    },
                    'type': 'upvote' if vote.value == 1 else 'downvote',
                    'created_at': vote.created_at
                })
            
            # Recent interactions (last 5)
            recent_interactions = PinInteraction.objects.filter(pin=pin).select_related('user').order_by('-created_at')[:5]
            for interaction in recent_interactions:
                profile_pic_url = None
                if interaction.user.profile_pic:
                    profile_pic_url = request.build_absolute_uri(interaction.user.profile_pic)
                    
                recent_activity.append({
                    'user': {
                        'id': interaction.user.id,
                        'username': interaction.user.username,
                        'profile_pic': profile_pic_url,
                    },
                    'type': interaction.interaction_type,
                    'created_at': interaction.created_at
                })
            
            # Sort by most recent
            recent_activity.sort(key=lambda x: x['created_at'], reverse=True)
            recent_activity = recent_activity[:10]  # Limit to 10 most recent
            
            # Calculate engagement rate (total interactions per view)
            total_interactions = counts['likes'] + counts['dislikes'] + counts['collects'] + counts['shares'] + counts['comments']
            engagement_rate = (total_interactions / counts['views'] * 100) if counts['views'] > 0 else 0
            
            # Check user's current vote
            user_vote = None
            if request.user.is_authenticated:
                vote = Vote.objects.filter(user=request.user, pin=pin).first()
                user_vote = vote.value if vote else None
            
            return Response({
                'pin_id': pin.id,
                'counts': counts,
                'engagement_rate': round(engagement_rate, 2),
                'total_interactions': total_interactions,
                'user_vote': user_vote,  # null, 1 (upvote), or -1 (downvote)
                'recent_activity': recent_activity,
                'is_owner': pin.owner == request.user,
                
                # ✅ NEW: Detailed user lists (like bop drops engagement)
                'detailed_engagement': {
                    'upvotes': upvotes_list,  # Users who liked (upvoted)
                    'downvotes': downvotes_list,  # Users who disliked (downvoted)
                    'views': views_list if pin.owner == request.user else [],  # Views only for pin owner
                    'comments': comments_list  # Comments with full content and user details
                },
                
                'notes': {
                    'likes_explanation': 'Likes = Upvotes from voting system (not like interactions)',
                    'dislikes_explanation': 'Dislikes = Downvotes from voting system',
                    'views_privacy': 'View details only shown to pin owner',
                    'counts_source': 'real_time_database_queries',
                    'detailed_lists': 'Now includes full user lists like bop drops engagement',
                    'cached_vs_realtime': f'Cached: {pin.upvote_count} upvotes, Real-time: {vote_stats.get("upvotes", 0)} upvotes'
                }
            })
            
        except Exception as e:
            logger.error(f"Error getting pin engagement: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def upvoted(self, request):
        """
        Get all pins that the current user has upvoted
        
        Query Parameters:
        - ordering: recent (default), oldest
        - page, page_size: for pagination
        """
        try:
            from votes.models import Vote
            
            # Get pins the user has upvoted using the model method
            upvoted_votes = Vote.get_user_upvoted_pins(
                user=request.user,
                ordering=request.query_params.get('ordering', 'recent')
            )
            
            # Get the pins from the votes
            pins = [vote.pin for vote in upvoted_votes]
            
            # Serialize the pins
            serializer = PinSerializer(pins, many=True, context={'request': request})
            
            # Add vote timestamps to the response
            response_data = serializer.data
            for i, pin_data in enumerate(response_data):
                pin_data['upvoted_at'] = upvoted_votes[i].created_at
            
            # Add pagination support
            page = self.paginate_queryset(response_data)
            if page is not None:
                paginated_response = self.get_paginated_response(page)
                paginated_response.data['sorting'] = {
                    'current_ordering': request.query_params.get('ordering', 'recent'),
                    'available_options': ['recent', 'oldest']
                }
                return paginated_response
            
            # Non-paginated response
            return Response({
                'results': response_data,
                'sorting': {
                    'current_ordering': request.query_params.get('ordering', 'recent'),
                    'available_options': ['recent', 'oldest']
                }
            })
            
        except Exception as e:
            logger.error(f"Error retrieving upvoted pins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


class PinInteractionViewSet(mixins.CreateModelMixin,
                           mixins.ListModelMixin,
                           mixins.RetrieveModelMixin,
                           viewsets.GenericViewSet):
    """
    API viewset for Pin Interactions
    Limited to create, list, and retrieve operations
    """
    queryset = PinInteraction.objects.all()
    serializer_class = PinInteractionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Only show the user's own interactions
        queryset = queryset.filter(user=self.request.user)
        
        # Filter by interaction type if provided
        interaction_type = self.request.query_params.get('type')
        if interaction_type:
            queryset = queryset.filter(interaction_type=interaction_type)
            
        return queryset
    
    def perform_create(self, serializer):
        """
        Set the user when creating an interaction
        """
        try:
            with transaction.atomic():
                interaction = serializer.save(user=self.request.user)
                
                # For collect interaction, increment the user's pins_collected count
                if interaction.interaction_type == 'collect':
                    self.request.user.increment_pins_collected()
                    
                logger.info(f"Created pin interaction: {interaction.user.username} {interaction.interaction_type} pin {interaction.pin.id}")
        except Exception as e:
            logger.error(f"Error creating pin interaction: {str(e)}")
            raise


class CollectionViewSet(BaseModelViewSet):
    """
    API viewset for Collection CRUD operations
    """
    queryset = Collection.objects.all()
    serializer_class = CollectionSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReadOnly]

    def _add_song_to_collection_safely(self, collection, pin=None, virtual_pin=None, user=None):
        """
        Helper method to safely add a song to a collection with proper error handling
        """
        if not pin and not virtual_pin:
            raise ValueError("Either pin or virtual_pin must be provided")
        if pin and virtual_pin:
            raise ValueError("Cannot provide both pin and virtual_pin")

        song_item = pin or virtual_pin
        song_type = "pin" if pin else "virtual_pin"

        # Check if song (by track_title + track_artist) is already in collection
        song_title = pin.track_title if pin else virtual_pin.track_title
        song_artist = pin.track_artist if pin else virtual_pin.track_artist

        existing_collection_pin = CollectionPin.objects.filter(
            collection=collection,
            track_title=song_title,
            track_artist=song_artist
        ).first()

        if existing_collection_pin:
            return {
                "success": False,
                "created": False,
                "error": "duplicate_song",
                "message": f"This song is already in your '{collection.name}' collection.",
                "details": {
                    "song_title": song_item.track_title,
                    "song_artist": song_item.track_artist,
                    "collection_name": collection.name,
                    "added_at": existing_collection_pin.added_at.isoformat()
                }
            }

        # Add the song to the collection with transaction safety
        try:
            with transaction.atomic():
                create_kwargs = {
                    'collection': collection,
                    'track_title': song_item.track_title,
                    'track_artist': song_item.track_artist
                }
                if pin:
                    create_kwargs.update({'pin': pin, 'virtual_pin': None})
                else:
                    create_kwargs.update({'virtual_pin': virtual_pin, 'pin': None})

                collection_pin = CollectionPin.objects.create(**create_kwargs)

                # Record interaction
                if pin and user:
                    record_pin_interaction(
                        user=user,
                        pin=pin,
                        interaction_type='collect'
                    )
                elif virtual_pin and user:
                    VirtualPinInteraction.objects.get_or_create(
                        user=user,
                        virtual_pin=virtual_pin,
                        interaction_type='collect'
                    )

                # Notify about collaboration if needed
                if collection.owner != user and user:
                    notify_collection_collaboration(
                        user=collection.owner,
                        collaborator=user,
                        collection_name=collection.name,
                        tracks_added=1
                    )

            return {
                "success": True,
                "created": True,
                "message": f"'{song_item.track_title}' by {song_item.track_artist} has been added to your '{collection.name}' collection.",
                "details": {
                    "song_title": song_item.track_title,
                    "song_artist": song_item.track_artist,
                    "collection_name": collection.name,
                    "added_at": collection_pin.added_at.isoformat()
                }
            }

        except IntegrityError:
            # Handle race condition where another request added the same song
            return {
                "success": False,
                "created": False,
                "error": "duplicate_song",
                "message": f"This song was just added to your '{collection.name}' collection by another request.",
                "details": {
                    "song_title": song_item.track_title,
                    "song_artist": song_item.track_artist,
                    "collection_name": collection.name
                }
            }
    
    def get_serializer_class(self):
        if self.action == 'retrieve':
            return CollectionDetailSerializer
        return CollectionSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter based on visibility (only show user's own private collections)
        if self.action in ['list']:
            queryset = queryset.filter(
                models.Q(is_public=True) | 
                models.Q(owner=self.request.user)
            )
        
        # Add annotated item_count
        queryset = queryset.annotate(
            item_count=models.Count('collection_pins')
        )
            
        return queryset
    
    def retrieve(self, request, *args, **kwargs):
        """
        Custom retrieve to check collection visibility before returning detail.
        Returns 404 if the collection is private and not owned by the requesting user.
        """
        try:
            instance = self.get_object()
            # Check if the collection should be visible to this user
            if instance.is_public == False and instance.owner != request.user:
                return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving collection: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def add_pin(self, request, pk=None):
        """
        Add a pin to a collection
        """
        collection = self.get_object()

        # Only the owner can add pins
        if collection.owner != request.user:
            return Response(
                {"detail": "You do not have permission to add pins to this collection."},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            
            # Get the pin ID from request data
            pin_id = request.data.get('pin_id')
            if not pin_id:
                return Response(
                    {"detail": "Pin ID is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if the pin exists
            try:
                pin = Pin.objects.get(id=pin_id)
            except Pin.DoesNotExist:
                return Response(
                    {"detail": "Pin not found."},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Check if the pin can be added to the collection
            if not pin.is_public and pin.owner != request.user:
                return Response(
                    {"detail": "You cannot add a private pin that you don't own."},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Use the helper method to safely add the pin
            result = self._add_song_to_collection_safely(
                collection=collection,
                pin=pin,
                user=request.user
            )

            if result["success"]:
                return Response(result)
            else:
                return Response(result, status=status.HTTP_409_CONFLICT)
            
        except Exception as e:
            logger.error(f"Error adding pin to collection: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def add_virtual_pin(self, request, pk=None):
        """
        Add a virtual pin to a collection
        """
        collection = self.get_object()

        # Only the owner can add virtual pins
        if collection.owner != request.user:
            return Response(
                {"detail": "You do not have permission to add virtual pins to this collection."},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            
            # Get the virtual pin ID from request data
            virtual_pin_id = request.data.get('virtual_pin_id')
            if not virtual_pin_id:
                return Response(
                    {"detail": "Virtual pin ID is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if the virtual pin exists
            try:
                virtual_pin = VirtualPin.objects.get(id=virtual_pin_id)
            except VirtualPin.DoesNotExist:
                return Response(
                    {"detail": "Virtual pin not found."},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Check if the virtual pin can be added to the collection
            if not virtual_pin.is_public and virtual_pin.owner != request.user:
                return Response(
                    {"detail": "You cannot add a private virtual pin that you don't own."},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Use the helper method to safely add the virtual pin
            result = self._add_song_to_collection_safely(
                collection=collection,
                virtual_pin=virtual_pin,
                user=request.user
            )

            if result["success"]:
                return Response(result)
            else:
                return Response(result, status=status.HTTP_409_CONFLICT)
            
        except Exception as e:
            logger.error(f"Error adding virtual pin to collection: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def remove_pin(self, request, pk=None):
        """
        Remove a pin or virtual pin from a collection
        """
        try:
            collection = self.get_object()
            
            # Only the owner can remove pins
            if collection.owner != request.user:
                return Response(
                    {"detail": "You do not have permission to remove pins from this collection."},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Get the pin ID or virtual pin ID from request data
            pin_id = request.data.get('pin_id')
            virtual_pin_id = request.data.get('virtual_pin_id')
            
            if not pin_id and not virtual_pin_id:
                return Response(
                    {"detail": "Either pin_id or virtual_pin_id is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if pin_id and virtual_pin_id:
                return Response(
                    {"detail": "Cannot specify both pin_id and virtual_pin_id."},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Try to find and delete the collection pin
            if pin_id:
                deleted, _ = CollectionPin.objects.filter(
                    collection=collection,
                    pin_id=pin_id,
                    virtual_pin__isnull=True
                ).delete()
                item_type = "Pin"
            else:
                deleted, _ = CollectionPin.objects.filter(
                    collection=collection,
                    virtual_pin_id=virtual_pin_id,
                    pin__isnull=True
                ).delete()
                item_type = "Virtual pin"
            
            if deleted:
                return Response({
                    "success": True,
                    "message": f"{item_type} removed from collection."
                })
            else:
                return Response({
                    "success": False,
                    "message": f"{item_type} was not in collection."
                })
            
        except Exception as e:
            logger.error(f"Error removing pin from collection: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def my_collections(self, request):
        """
        Get only the current user's collections
        """
        try:
            collections = Collection.objects.filter(owner=request.user).annotate(
                item_count=models.Count('collection_pins')
            )
            serializer = self.get_serializer(collections, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving user collections: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def check_song_status(self, request, pk=None):
        """
        Check if a pin or virtual pin is already in the collection
        """
        try:
            collection = self.get_object()

            # Get the pin ID or virtual pin ID from query parameters
            pin_id = request.query_params.get('pin_id')
            virtual_pin_id = request.query_params.get('virtual_pin_id')

            if not pin_id and not virtual_pin_id:
                return Response(
                    {"detail": "Either pin_id or virtual_pin_id query parameter is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check for pin
            if pin_id:
                try:
                    pin = Pin.objects.get(id=pin_id)
                    existing_collection_pin = CollectionPin.objects.filter(
                        collection=collection,
                        track_title=pin.track_title,
                        track_artist=pin.track_artist
                    ).first()

                    if existing_collection_pin:
                        return Response({
                            "in_collection": True,
                            "song_type": "pin",
                            "song_title": pin.track_title,
                            "song_artist": pin.track_artist,
                            "added_at": existing_collection_pin.added_at.isoformat(),
                            "collection_name": collection.name
                        })
                    else:
                        return Response({
                            "in_collection": False,
                            "song_type": "pin",
                            "song_title": pin.track_title,
                            "song_artist": pin.track_artist,
                            "collection_name": collection.name
                        })

                except Pin.DoesNotExist:
                    return Response(
                        {"detail": "Pin not found."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            # Check for virtual pin
            if virtual_pin_id:
                try:
                    virtual_pin = VirtualPin.objects.get(id=virtual_pin_id)
                    existing_collection_pin = CollectionPin.objects.filter(
                        collection=collection,
                        track_title=virtual_pin.track_title,
                        track_artist=virtual_pin.track_artist
                    ).first()

                    if existing_collection_pin:
                        return Response({
                            "in_collection": True,
                            "song_type": "virtual_pin",
                            "song_title": virtual_pin.track_title,
                            "song_artist": virtual_pin.track_artist,
                            "added_at": existing_collection_pin.added_at.isoformat(),
                            "collection_name": collection.name
                        })
                    else:
                        return Response({
                            "in_collection": False,
                            "song_type": "virtual_pin",
                            "song_title": virtual_pin.track_title,
                            "song_artist": virtual_pin.track_artist,
                            "collection_name": collection.name
                        })

                except VirtualPin.DoesNotExist:
                    return Response(
                        {"detail": "Virtual pin not found."},
                        status=status.HTTP_404_NOT_FOUND
                    )

        except Exception as e:
            logger.error(f"Error checking song status in collection: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def check_songs_status(self, request, pk=None):
        """
        Check multiple pins/virtual pins status in the collection at once
        """
        try:
            collection = self.get_object()

            # Get lists of pin IDs and virtual pin IDs from request data
            pin_ids = request.data.get('pin_ids', [])
            virtual_pin_ids = request.data.get('virtual_pin_ids', [])

            if not pin_ids and not virtual_pin_ids:
                return Response(
                    {"detail": "Either pin_ids or virtual_pin_ids array is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            results = []

            # Check pins
            if pin_ids:
                pins = Pin.objects.filter(id__in=pin_ids)

                for pin in pins:
                    existing_collection_pin = CollectionPin.objects.filter(
                        collection=collection,
                        track_title=pin.track_title,
                        track_artist=pin.track_artist
                    ).first()

                    results.append({
                        "id": pin.id,
                        "song_type": "pin",
                        "song_title": pin.track_title,
                        "song_artist": pin.track_artist,
                        "in_collection": existing_collection_pin is not None,
                        "added_at": existing_collection_pin.added_at.isoformat() if existing_collection_pin else None
                    })

            # Check virtual pins
            if virtual_pin_ids:
                virtual_pins = VirtualPin.objects.filter(id__in=virtual_pin_ids)

                for virtual_pin in virtual_pins:
                    existing_collection_pin = CollectionPin.objects.filter(
                        collection=collection,
                        track_title=virtual_pin.track_title,
                        track_artist=virtual_pin.track_artist
                    ).first()

                    results.append({
                        "id": virtual_pin.id,
                        "song_type": "virtual_pin",
                        "song_title": virtual_pin.track_title,
                        "song_artist": virtual_pin.track_artist,
                        "in_collection": existing_collection_pin is not None,
                        "added_at": existing_collection_pin.added_at.isoformat() if existing_collection_pin else None
                    })

            return Response({
                "collection_name": collection.name,
                "songs": results
            })

        except Exception as e:
            logger.error(f"Error checking songs status in collection: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


class VirtualPinViewSet(BaseModelViewSet):
    """
    API viewset for VirtualPin CRUD operations
    """
    queryset = VirtualPin.objects.all()
    serializer_class = VirtualPinSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReadOnly]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter expired virtual pins
        queryset = queryset.filter(
            models.Q(expiration_date__isnull=True) | 
            models.Q(expiration_date__gt=timezone.now())
        )
        
        # Filter private virtual pins (only show user's own private virtual pins)
        if self.action in ['list']:
            queryset = queryset.filter(
                models.Q(is_private=False) | 
                models.Q(owner=self.request.user)
            )
        
        return queryset
    
    def retrieve(self, request, *args, **kwargs):
        """
        Custom retrieve to check virtual pin visibility before returning detail.
        Returns 404 if the virtual pin is private and not owned by the requesting user.
        """
        try:
            instance = self.get_object()
            # Check if the virtual pin should be visible to this user
            if instance.is_private and instance.owner != request.user:
                return Response({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving virtual pin: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """
        Record a like interaction with a virtual pin
        """
        return self._record_virtual_interaction(request, pk, 'like')
    
    @action(detail=True, methods=['post'])
    def collect(self, request, pk=None):
        """
        Record a collect interaction with a virtual pin
        """
        return self._record_virtual_interaction(request, pk, 'collect')
    
    @action(detail=True, methods=['post'])
    def view(self, request, pk=None):
        """
        Record a view interaction with a virtual pin
        """
        return self._record_virtual_interaction(request, pk, 'view')
    
    @action(detail=True, methods=['post'])
    def share(self, request, pk=None):
        """
        Record a share interaction with a virtual pin
        """
        return self._record_virtual_interaction(request, pk, 'share')
    
    def _record_virtual_interaction(self, request, pk, interaction_type):
        """
        Helper method to record virtual pin interactions
        """
        try:
            virtual_pin = self.get_object()
            
            # Check if the virtual pin is visible to the user
            if virtual_pin.is_private and virtual_pin.owner != request.user:
                return create_error_response("Virtual pin is not available", status.HTTP_404_NOT_FOUND)
            
            # Record the interaction
            interaction, created = VirtualPinInteraction.objects.get_or_create(
                user=request.user,
                virtual_pin=virtual_pin,
                interaction_type=interaction_type
            )
            
            # For collect interaction, increment the user's pins_collected count
            if interaction_type == 'collect' and created:
                with transaction.atomic():
                    request.user.increment_pins_collected()
                    
            return Response({
                "success": True,
                "created": created,
                "message": f"Virtual pin {interaction_type} recorded successfully"
            })
            
        except Exception as e:
            logger.error(f"Error recording virtual pin {interaction_type}: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


class PinSkinViewSet(BaseReadOnlyViewSet):
    """
    API viewset for PinSkin with new unlock/claim system
    """
    queryset = PinSkin.objects.all()
    serializer_class = PinSkinSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['GET'])
    def available(self, request):
        """
        Get all available skins with lock status for current user
        """
        queryset = self.get_queryset()
        
        # Add active challenge skins
        queryset = queryset.filter(
            models.Q(skin_type=PinSkin.HOUSE) |
            models.Q(challenge__is_active=True)
        )
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['GET'])
    def featured(self, request):
        """
        Get featured skins: latest pins, premium pins, top 15
        """
        try:
            # Get latest pins (last 30 days)
            from datetime import timedelta
            thirty_days_ago = timezone.now() - timedelta(days=30)
            
            latest_pins = self.get_queryset().filter(
                created_at__gte=thirty_days_ago,
                expiry_date__isnull=True  # Exclude limited-time pins
            ).order_by('-created_at')[:10]
            
            # Get premium pins
            premium_pins = self.get_queryset().filter(
                is_premium=True,
                expiry_date__isnull=True  # Exclude limited-time pins
            ).order_by('-created_at')[:8]
            
            # Get top pins (most popular by UserSkin count)
            from django.db.models import Count
            top_pins = self.get_queryset().annotate(
                unlock_count=Count('userskin')
            ).filter(
                expiry_date__isnull=True  # Exclude limited-time pins
            ).order_by('-unlock_count', '-created_at')[:15]
            
            # Serialize each category
            latest_serializer = self.get_serializer(latest_pins, many=True)
            premium_serializer = self.get_serializer(premium_pins, many=True)
            top_serializer = self.get_serializer(top_pins, many=True)
            
            return Response({
                'latest': latest_serializer.data,
                'premium': premium_serializer.data,
                'top': top_serializer.data,
                'metadata': {
                    'latest_count': latest_pins.count(),
                    'premium_count': premium_pins.count(),
                    'top_count': top_pins.count(),
                    'generated_at': timezone.now()
                }
            })
            
        except Exception as e:
            logger.error(f"Error in featured skins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['GET'])
    def limited(self, request):
        """
        Get all limited-time skins that could expire, include expiry date in response
        """
        try:
            # Get all skins with expiry dates
            limited_skins = self.get_queryset().filter(
                expiry_date__isnull=False
            ).order_by('expiry_date')  # Sort by expiry date (soonest first)
            
            # Separate active and expired
            active_limited = limited_skins.filter(
                expiry_date__gt=timezone.now()
            )
            
            expired_limited = limited_skins.filter(
                expiry_date__lte=timezone.now()
            )
            
            # Serialize
            active_serializer = self.get_serializer(active_limited, many=True)
            expired_serializer = self.get_serializer(expired_limited, many=True)
            
            return Response({
                'active': active_serializer.data,
                'expired': expired_serializer.data,
                'metadata': {
                    'active_count': active_limited.count(),
                    'expired_count': expired_limited.count(),
                    'total_limited': limited_skins.count(),
                    'checked_at': timezone.now()
                }
            })
            
        except Exception as e:
            logger.error(f"Error in limited skins: {str(e)}")
            return create_error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['GET'])
    def unlocked(self, request):
        """
        Get only the skins that the current user has unlocked
        """
        user = request.user
        
        # Get skins user has unlocked through UserSkin
        unlocked_skin_ids = UserSkin.objects.filter(user=user).values_list('skin_id', flat=True)
        
        # Also include non-premium house skins (always available)
        queryset = self.get_queryset().filter(
            models.Q(id__in=unlocked_skin_ids) |
            models.Q(skin_type=PinSkin.HOUSE, is_premium=False)
        )
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['POST'])
    def claim(self, request, pk=None):
        """
        Manual claim: if user is eligible, create UserSkin
        """
        skin = self.get_object()
        user = request.user
        
        # Check if skin has expired
        if skin.is_expired:
            return Response(
                {"error": f"Skin '{skin.name}' has expired and is no longer available"},
                status=status.HTTP_410_GONE
            )
        
        # Check if already unlocked
        if UserSkin.objects.filter(user=user, skin=skin).exists():
            return Response(
                {"success": True, "message": f"Skin '{skin.name}' already unlocked"},
                status=status.HTTP_200_OK
            )
        
        # Check if this is a free house skin (automatically available to all users)
        if skin.skin_type == PinSkin.HOUSE and not skin.is_premium:
            return Response(
                {"success": True, "message": f"Skin '{skin.name}' already unlocked"},
                status=status.HTTP_200_OK
            )
        
        # Check eligibility
        eligible = False
        reason = ""
        
        if skin.skin_type == PinSkin.HOUSE:
            if skin.is_premium:
                # Check if user has premium subscription (placeholder logic)
                eligible = user.is_premium if hasattr(user, 'is_premium') else False
                reason = "Premium subscription required"
        elif skin.challenge:
            # Check if user participated in the challenge
            participated = ChallengeParticipation.objects.filter(
                user=user, 
                challenge=skin.challenge
            ).exists()
            
            if participated and skin.challenge.has_ended:
                metadata = skin.metadata or {}
                unlock_type = metadata.get('unlock_type', 'PARTICIPATE')
                
                if unlock_type == 'PARTICIPATE':
                    eligible = True
                elif unlock_type == 'TOP_N':
                    # Check if user is in top N
                    n = metadata.get('n', 3)
                    top_participants = ChallengeParticipation.objects.filter(
                        challenge=skin.challenge
                    ).order_by('-vote_score')[:n]
                    
                    eligible = user in [p.user for p in top_participants]
                    reason = f"Must be in top {n} participants"
            else:
                reason = "Must participate in challenge and wait for it to end"
        elif skin.achievement:
            # Check if user has completed the achievement
            from gamification.models import UserAchievement
            user_achievement = UserAchievement.objects.filter(
                user=user,
                achievement=skin.achievement,
                completed_at__isnull=False
            ).first()
            
            if user_achievement:
                eligible = True
            else:
                reason = f"Must complete achievement: {skin.achievement.name}"
        
        if not eligible:
            return Response(
                {"error": f"Not eligible to claim this skin. {reason}"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        try:
            UserSkin.objects.create(user=user, skin=skin)
            return Response(
                {"success": True, "message": f"Successfully claimed skin: {skin.name}"},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Error claiming skin: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['POST'])
    def equip(self, request, pk=None):
        """
        Equip a skin for the current user (if unlocked)
        """
        skin = self.get_object()
        user = request.user
        
        # Check if skin has expired
        if skin.is_expired:
            return Response(
                {"error": f"Skin '{skin.name}' has expired and cannot be equipped"},
                status=status.HTTP_410_GONE
            )
        
        # Check if user has unlocked this skin
        is_unlocked = (
            UserSkin.objects.filter(user=user, skin=skin).exists() or
            (skin.skin_type == PinSkin.HOUSE and not skin.is_premium)
        )
        
        if not is_unlocked:
            return Response(
                {"error": "You don't have access to this skin"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        try:
            # Set this skin as the user's current skin
            user.current_pin_skin = skin
            user.save(update_fields=['current_pin_skin'])
            
            return Response(
                {"success": True, "message": f"Equipped skin: {skin.name}"},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Error equipping skin: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class WeeklyChallengeViewSet(BaseReadOnlyViewSet):
    """
    API viewset for WeeklyChallenge - Read only for users
    """
    queryset = WeeklyChallenge.objects.filter(is_active=True)
    serializer_class = WeeklyChallengeSerializer
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['GET'])
    def active(self, request):
        """
        Get currently active challenges
        """
        queryset = self.get_queryset().filter(
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        )
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['GET'])
    def upcoming(self, request):
        """
        Get upcoming challenges
        """
        queryset = self.get_queryset().filter(
            start_date__gt=timezone.now()
        ).order_by('start_date')
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class ChallengeParticipationViewSet(BaseModelViewSet):
    """
    API viewset for ChallengeParticipation
    """
    serializer_class = ChallengeParticipationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Return only current user's participations
        """
        return ChallengeParticipation.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        """
        Ensure the user is set to the current user and auto-unlock participation skins
        """
        participation = serializer.save(user=self.request.user)
        
        # Check for auto-unlock skins
        challenge = participation.challenge
        for skin in challenge.reward_skins.all():
            metadata = skin.metadata or {}
            unlock_type = metadata.get('unlock_type', 'PARTICIPATE')
            
            if unlock_type == 'PARTICIPATE':
                # Auto-unlock immediately on participation
                UserSkin.objects.get_or_create(user=self.request.user, skin=skin)
                logger.info(f"Auto-unlocked skin {skin.name} for user {self.request.user.username}")


class UserSkinViewSet(BaseReadOnlyViewSet):
    """
    API viewset for UserSkin - Read only
    """
    serializer_class = UserSkinSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Return only current user's unlocked skins
        """
        return UserSkin.objects.filter(user=self.request.user)

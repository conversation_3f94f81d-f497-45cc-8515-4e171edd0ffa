"""
Pin-related services for BOPMaps
"""
import logging
from typing import Dict, Optional
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger('bopmaps')

class GeocodingService:
    """
    Service for reverse geocoding pin coordinates to extract geographic information
    """
    
    @staticmethod
    def reverse_geocode(latitude: float, longitude: float, location_name: str = None) -> Dict[str, Optional[str]]:
        """
        Extract geographic information from coordinates with fallback to location_name parsing
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            location_name: Human-readable location name as fallback
            
        Returns:
            Dictionary with city, state, country, continent information
        """
        # Create cache key for this location (rounded to ~1km precision)
        cache_key = f"geocode_{round(latitude, 3)}_{round(longitude, 3)}"
        
        # Check cache first
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Initialize default geo data
        geo_data = {
            'city': None,
            'state': None,
            'country': None,
            'continent': None,
            'district': None,
            'population': None
        }
        
        # Try full geocoding first
        try:
            import geocoder
            
            # Try with OSM provider with proper user agent
            location = geocoder.osm((latitude, longitude), method='reverse', 
                                  headers={'User-Agent': 'BOPMaps/1.0'})
            
            if location and location.ok:
                geo_data['city'] = (
                    location.city or 
                    location.town or 
                    location.village or 
                    location.suburb
                )
                geo_data['state'] = (
                    location.state or 
                    location.province or 
                    location.region
                )
                geo_data['country'] = location.country
                geo_data['district'] = (
                    getattr(location, 'neighbourhood', None) or 
                    location.suburb or 
                    getattr(location, 'district', None)
                )
                
                if geo_data['country']:
                    geo_data['continent'] = GeocodingService._get_continent(geo_data['country'])
                
                # Cache successful result
                cache.set(cache_key, geo_data, timeout=86400)
                logger.info(f"Geocoded ({latitude}, {longitude}) -> {geo_data['city']}, {geo_data['country']}")
                return geo_data
                
        except Exception as e:
            logger.warning(f"Geocoding failed for ({latitude}, {longitude}): {str(e)}")
        
        # Fallback: Parse location_name if available
        if location_name:
            parsed_data = GeocodingService._parse_location_name(location_name)
            for key, value in parsed_data.items():
                if value:
                    geo_data[key] = value
            
            logger.info(f"Parsed location_name '{location_name}' -> {geo_data['city']}, {geo_data['country']}")
        
        # Cache the result (shorter time for fallback)
        cache.set(cache_key, geo_data, timeout=3600)
        return geo_data
    
    @staticmethod
    def _parse_location_name(location_name: str) -> Dict[str, Optional[str]]:
        """
        Parse location_name field to extract geographic information
        
        Args:
            location_name: Human-readable location string
            
        Returns:
            Dictionary with parsed geographic information
        """
        if not location_name:
            return {}
        
        # Common location patterns to parse
        parsed = {
            'city': None,
            'state': None,
            'country': None,
            'continent': None,
            'district': None
        }
        
        # Split by common delimiters
        parts = [part.strip() for part in location_name.replace(',', '|').replace(';', '|').split('|')]
        
        # US state codes mapping
        us_states = {
            'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas', 'CA': 'California',
            'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware', 'FL': 'Florida', 'GA': 'Georgia',
            'HI': 'Hawaii', 'ID': 'Idaho', 'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa',
            'KS': 'Kansas', 'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
            'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi', 'MO': 'Missouri',
            'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada', 'NH': 'New Hampshire', 'NJ': 'New Jersey',
            'NM': 'New Mexico', 'NY': 'New York', 'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio',
            'OK': 'Oklahoma', 'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
            'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah', 'VT': 'Vermont',
            'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia', 'WI': 'Wisconsin', 'WY': 'Wyoming'
        }
        
        # Common patterns:
        # "New York, NY, USA"
        # "Charlotte, NC" 
        # "London, UK"
        # "Times Square, New York"
        
        if len(parts) >= 3:
            # Three parts: City, State, Country
            parsed['city'] = parts[0].strip()
            
            # Check if second part is US state
            state_part = parts[1].upper().strip()
            if state_part in us_states:
                parsed['state'] = us_states[state_part]
                parsed['country'] = 'United States'
                parsed['continent'] = 'NA'
            else:
                parsed['state'] = parts[1].strip()
            
            # Handle explicit country in third part
            country_part = parts[2].upper().strip()
            if country_part in ['USA', 'US', 'UNITED STATES']:
                parsed['country'] = 'United States'
                parsed['continent'] = 'NA'
            elif country_part in ['UK', 'UNITED KINGDOM', 'ENGLAND', 'SCOTLAND', 'WALES']:
                parsed['country'] = 'United Kingdom'
                parsed['continent'] = 'EU'
            elif country_part in ['CANADA', 'CA']:
                parsed['country'] = 'Canada'
                parsed['continent'] = 'NA'
            # Add more countries as needed
        
        elif len(parts) == 2:
            # Two parts: Could be "City, State" or "City, Country"
            first_part = parts[0].strip()
            second_part = parts[1].upper().strip()
            
            # Check if second part is a US state code
            if second_part in us_states:
                # Pattern: "Charlotte, NC" -> US location
                parsed['city'] = first_part
                parsed['state'] = us_states[second_part]
                parsed['country'] = 'United States'
                parsed['continent'] = 'NA'
            elif second_part in ['USA', 'US', 'UNITED STATES']:
                # Pattern: "New York, USA"
                parsed['city'] = first_part
                parsed['country'] = 'United States'
                parsed['continent'] = 'NA'
            elif second_part in ['UK', 'UNITED KINGDOM', 'ENGLAND', 'SCOTLAND', 'WALES']:
                parsed['city'] = first_part
                parsed['country'] = 'United Kingdom'
                parsed['continent'] = 'EU'
            elif second_part in ['CANADA', 'CA']:
                parsed['city'] = first_part
                parsed['country'] = 'Canada'
                parsed['continent'] = 'NA'
            elif second_part in ['FRANCE', 'FR']:
                parsed['city'] = first_part
                parsed['country'] = 'France'
                parsed['continent'] = 'EU'
            elif second_part in ['GERMANY', 'DE']:
                parsed['city'] = first_part
                parsed['country'] = 'Germany'
                parsed['continent'] = 'EU'
            elif second_part in ['JAPAN', 'JP']:
                parsed['city'] = first_part
                parsed['country'] = 'Japan'
                parsed['continent'] = 'AS'
            elif second_part in ['AUSTRALIA', 'AU']:
                parsed['city'] = first_part
                parsed['country'] = 'Australia'
                parsed['continent'] = 'OC'
            elif second_part in ['BRAZIL', 'BR']:
                parsed['city'] = first_part
                parsed['country'] = 'Brazil'
                parsed['continent'] = 'SA'
            else:
                # Unknown pattern - treat as City, Region but don't assume country
                parsed['city'] = first_part
                parsed['state'] = parts[1].strip()
        
        elif len(parts) == 1:
            # Single part - just a city name
            parsed['city'] = parts[0].strip()
        
        return parsed
    
    @staticmethod
    def _get_continent(country: str) -> Optional[str]:
        """
        Map country to continent code
        
        Args:
            country: Country name
            
        Returns:
            Continent code (NA, SA, EU, AF, AS, OC) or None
        """
        # Simplified mapping - could be expanded with a more comprehensive list
        continent_mapping = {
            # North America
            'United States': 'NA', 'USA': 'NA', 'Canada': 'NA', 'Mexico': 'NA',
            'Guatemala': 'NA', 'Belize': 'NA', 'Honduras': 'NA', 'El Salvador': 'NA',
            'Nicaragua': 'NA', 'Costa Rica': 'NA', 'Panama': 'NA', 'Cuba': 'NA',
            'Haiti': 'NA', 'Dominican Republic': 'NA', 'Jamaica': 'NA', 'Bahamas': 'NA',
            
            # South America  
            'Brazil': 'SA', 'Argentina': 'SA', 'Chile': 'SA', 'Peru': 'SA',
            'Colombia': 'SA', 'Venezuela': 'SA', 'Ecuador': 'SA', 'Bolivia': 'SA',
            'Paraguay': 'SA', 'Uruguay': 'SA', 'Guyana': 'SA', 'Suriname': 'SA',
            
            # Europe
            'United Kingdom': 'EU', 'UK': 'EU', 'France': 'EU', 'Germany': 'EU',
            'Italy': 'EU', 'Spain': 'EU', 'Portugal': 'EU', 'Netherlands': 'EU',
            'Belgium': 'EU', 'Switzerland': 'EU', 'Austria': 'EU', 'Sweden': 'EU',
            'Norway': 'EU', 'Denmark': 'EU', 'Finland': 'EU', 'Poland': 'EU',
            'Czech Republic': 'EU', 'Hungary': 'EU', 'Romania': 'EU', 'Bulgaria': 'EU',
            'Greece': 'EU', 'Croatia': 'EU', 'Serbia': 'EU', 'Bosnia': 'EU',
            'Albania': 'EU', 'Montenegro': 'EU', 'North Macedonia': 'EU', 'Slovenia': 'EU',
            'Slovakia': 'EU', 'Estonia': 'EU', 'Latvia': 'EU', 'Lithuania': 'EU',
            'Belarus': 'EU', 'Ukraine': 'EU', 'Moldova': 'EU', 'Russia': 'EU',
            'Ireland': 'EU', 'Iceland': 'EU', 'Luxembourg': 'EU', 'Malta': 'EU',
            'Cyprus': 'EU', 'Monaco': 'EU', 'Liechtenstein': 'EU', 'San Marino': 'EU',
            'Andorra': 'EU', 'Vatican': 'EU',
            
            # Africa
            'South Africa': 'AF', 'Nigeria': 'AF', 'Egypt': 'AF', 'Kenya': 'AF',
            'Ghana': 'AF', 'Morocco': 'AF', 'Algeria': 'AF', 'Tunisia': 'AF',
            'Libya': 'AF', 'Sudan': 'AF', 'Ethiopia': 'AF', 'Uganda': 'AF',
            'Tanzania': 'AF', 'Mozambique': 'AF', 'Madagascar': 'AF', 'Angola': 'AF',
            'Zambia': 'AF', 'Zimbabwe': 'AF', 'Botswana': 'AF', 'Namibia': 'AF',
            'Malawi': 'AF', 'Rwanda': 'AF', 'Burundi': 'AF', 'Central African Republic': 'AF',
            'Chad': 'AF', 'Niger': 'AF', 'Mali': 'AF', 'Burkina Faso': 'AF',
            'Ivory Coast': 'AF', 'Guinea': 'AF', 'Senegal': 'AF', 'Gambia': 'AF',
            'Sierra Leone': 'AF', 'Liberia': 'AF', 'Togo': 'AF', 'Benin': 'AF',
            'Cameroon': 'AF', 'Equatorial Guinea': 'AF', 'Gabon': 'AF', 'Congo': 'AF',
            'Democratic Republic of the Congo': 'AF', 'Somalia': 'AF', 'Djibouti': 'AF',
            'Eritrea': 'AF', 'Lesotho': 'AF', 'Swaziland': 'AF', 'Mauritius': 'AF',
            'Seychelles': 'AF', 'Comoros': 'AF', 'Cape Verde': 'AF', 'São Tomé and Príncipe': 'AF',
            
            # Asia
            'China': 'AS', 'India': 'AS', 'Japan': 'AS', 'South Korea': 'AS',
            'North Korea': 'AS', 'Thailand': 'AS', 'Vietnam': 'AS', 'Philippines': 'AS',
            'Indonesia': 'AS', 'Malaysia': 'AS', 'Singapore': 'AS', 'Myanmar': 'AS',
            'Cambodia': 'AS', 'Laos': 'AS', 'Bangladesh': 'AS', 'Pakistan': 'AS',
            'Afghanistan': 'AS', 'Iran': 'AS', 'Iraq': 'AS', 'Turkey': 'AS',
            'Syria': 'AS', 'Lebanon': 'AS', 'Jordan': 'AS', 'Israel': 'AS',
            'Palestine': 'AS', 'Saudi Arabia': 'AS', 'Yemen': 'AS', 'Oman': 'AS',
            'UAE': 'AS', 'Qatar': 'AS', 'Bahrain': 'AS', 'Kuwait': 'AS',
            'Mongolia': 'AS', 'Kazakhstan': 'AS', 'Uzbekistan': 'AS', 'Turkmenistan': 'AS',
            'Kyrgyzstan': 'AS', 'Tajikistan': 'AS', 'Nepal': 'AS', 'Bhutan': 'AS',
            'Sri Lanka': 'AS', 'Maldives': 'AS', 'Brunei': 'AS', 'East Timor': 'AS',
            'Armenia': 'AS', 'Azerbaijan': 'AS', 'Georgia': 'AS',
            
            # Oceania
            'Australia': 'OC', 'New Zealand': 'OC', 'Papua New Guinea': 'OC',
            'Fiji': 'OC', 'Solomon Islands': 'OC', 'Vanuatu': 'OC', 'Samoa': 'OC',
            'Tonga': 'OC', 'Micronesia': 'OC', 'Palau': 'OC', 'Marshall Islands': 'OC',
            'Nauru': 'OC', 'Kiribati': 'OC', 'Tuvalu': 'OC'
        }
        
        return continent_mapping.get(country) 
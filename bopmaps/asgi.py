"""
ASGI config for bopmaps project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
from channels.routing import ProtocolTypeRouter, URLRouter
from django.core.asgi import get_asgi_application
from .websocket_middleware import JWTAuthMiddlewareStack
import geo.routing
import friends.routing
import gamification.routing

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bopmaps.settings")

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": JWTAuthMiddlewareStack(
        URLRouter(
            geo.routing.websocket_urlpatterns +
            friends.routing.websocket_urlpatterns +
            gamification.routing.websocket_urlpatterns
        )
    ),
})

from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async
from urllib.parse import parse_qs
import logging

logger = logging.getLogger(__name__)


@database_sync_to_async
def get_user_from_token(token_string):
    """
    Get user from JWT token
    """
    # Import Django models inside the function to avoid AppRegistryNotReady
    from django.contrib.auth.models import AnonymousUser
    from django.contrib.auth import get_user_model
    from rest_framework_simplejwt.tokens import AccessToken
    from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
    
    User = get_user_model()
    
    try:
        # Validate the token
        token = AccessToken(token_string)
        user_id = token.get('user_id')
        
        # Get the user
        user = User.objects.get(id=user_id)
        return user
    except (InvalidToken, TokenError, User.DoesNotExist) as e:
        logger.warning(f"WebSocket JWT authentication failed: {str(e)}")
        return AnonymousUser()


class JWTAuthMiddleware(BaseMiddleware):
    """
    Custom middleware to authenticate WebSocket connections using JWT tokens.
    
    The token can be provided in:
    1. Query parameter: ?token=your_jwt_token
    2. Headers: Authorization: Bearer your_jwt_token
    """
    
    async def __call__(self, scope, receive, send):
        # Import AnonymousUser inside the method to avoid AppRegistryNotReady
        from django.contrib.auth.models import AnonymousUser
        
        # Only process WebSocket connections
        if scope['type'] != 'websocket':
            return await super().__call__(scope, receive, send)
        
        # Try to get token from query parameters first
        token = None
        query_string = scope.get('query_string', b'').decode()
        if query_string:
            query_params = parse_qs(query_string)
            token_list = query_params.get('token', [])
            if token_list:
                token = token_list[0]
        
        # If no token in query params, try headers
        if not token:
            headers = dict(scope.get('headers', []))
            auth_header = headers.get(b'authorization', b'').decode()
            if auth_header.startswith('Bearer '):
                token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Authenticate user with token
        if token:
            scope['user'] = await get_user_from_token(token)
        else:
            scope['user'] = AnonymousUser()
        
        return await super().__call__(scope, receive, send)


def JWTAuthMiddlewareStack(inner):
    """
    Convenience function to wrap the JWT auth middleware
    """
    return JWTAuthMiddleware(inner) 
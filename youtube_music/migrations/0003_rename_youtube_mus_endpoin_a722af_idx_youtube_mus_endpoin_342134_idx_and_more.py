# Generated by Django 4.2.7 on 2025-07-30 22:33

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("youtube_music", "0002_create_missing_tables"),
    ]

    operations = [
        migrations.RenameIndex(
            model_name="ytmusiccacheentry",
            new_name="youtube_mus_endpoin_342134_idx",
            old_name="youtube_mus_endpoin_a722af_idx",
        ),
        migrations.RenameIndex(
            model_name="ytmusiccacheentry",
            new_name="youtube_mus_created_a19d24_idx",
            old_name="youtube_mus_created_71606f_idx",
        ),
        migrations.RenameIndex(
            model_name="ytmusiccacheentry",
            new_name="youtube_mus_last_ac_5ea75e_idx",
            old_name="youtube_mus_last_ac_ab98d6_idx",
        ),
        migrations.AlterModelTable(
            name="ytmusiccacheentry",
            table="youtube_music_youtubemusiccacheentry",
        ),
    ]

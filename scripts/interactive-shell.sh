#!/bin/bash

# Interactive Shell Script for ECS
# This script starts a one-off task that stays alive for interactive access

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PROJECT_NAME="bopmaps"
ENVIRONMENT="prod"
AWS_REGION="us-east-1"

print_status "Starting interactive shell session..."

# Get network configuration from Terraform outputs
cd terraform
PRIVATE_SUBNET_ID=$(terraform output -json private_subnet_ids | jq -r '.[0]')
ECS_SECURITY_GROUP_ID=$(terraform output -raw ecs_security_group_id)
cd ..

print_status "Starting one-off task for interactive shell..."

# Start a task that stays alive
TASK_ARN=$(aws ecs run-task \
    --cluster "$PROJECT_NAME-$ENVIRONMENT" \
    --task-definition "$PROJECT_NAME-$ENVIRONMENT-app" \
    --network-configuration "awsvpcConfiguration={subnets=[\"$PRIVATE_SUBNET_ID\"],securityGroups=[\"$ECS_SECURITY_GROUP_ID\"]}" \
    --overrides '{"containerOverrides":[{"name":"app","command":["/bin/bash","-c","tail -f /dev/null"]}]}' \
    --region "$AWS_REGION" \
    --query 'tasks[0].taskArn' \
    --output text)

if [ -z "$TASK_ARN" ]; then
    print_error "Failed to start task"
    exit 1
fi

print_status "Task started: $TASK_ARN"
print_status "Waiting for task to be running..."

# Wait for task to be running
aws ecs wait tasks-running --cluster "$PROJECT_NAME-$ENVIRONMENT" --tasks "$TASK_ARN" --region "$AWS_REGION"

print_status "Task is running! Now attempting to connect..."

# Try to connect with ECS Exec
print_status "Attempting to connect with ECS Exec..."
if aws ecs execute-command --cluster "$PROJECT_NAME-$ENVIRONMENT" --task "$TASK_ARN" --container app --interactive --command "/bin/bash" --region "$AWS_REGION"; then
    print_status "Interactive session ended"
else
    print_warning "ECS Exec failed. You can still run commands using one-off tasks:"
    echo ""
    echo "To create a superuser:"
    echo "aws ecs run-task --cluster $PROJECT_NAME-$ENVIRONMENT --task-definition $PROJECT_NAME-$ENVIRONMENT-app --network-configuration \"awsvpcConfiguration={subnets=[\"$PRIVATE_SUBNET_ID\"],securityGroups=[\"$ECS_SECURITY_GROUP_ID\"]}\" --overrides '{\"containerOverrides\":[{\"name\":\"app\",\"command\":[\"python\",\"manage.py\",\"createsuperuser\",\"--noinput\",\"--username\",\"admin\",\"--email\",\"<EMAIL>\"]}]}'"
    echo ""
    echo "To run Django shell:"
    echo "aws ecs run-task --cluster $PROJECT_NAME-$ENVIRONMENT --task-definition $PROJECT_NAME-$ENVIRONMENT-app --network-configuration \"awsvpcConfiguration={subnets=[\"$PRIVATE_SUBNET_ID\"],securityGroups=[\"$ECS_SECURITY_GROUP_ID\"]}\" --overrides '{\"containerOverrides\":[{\"name\":\"app\",\"command\":[\"python\",\"manage.py\",\"shell\"]}]}'"
    echo ""
    echo "To check logs:"
    echo "aws logs tail /ecs/$PROJECT_NAME-$ENVIRONMENT/app --follow"
fi

# Clean up the task
print_status "Cleaning up task..."
aws ecs stop-task --cluster "$PROJECT_NAME-$ENVIRONMENT" --task "$TASK_ARN" --region "$AWS_REGION" > /dev/null 2>&1

print_status "Interactive session completed" 
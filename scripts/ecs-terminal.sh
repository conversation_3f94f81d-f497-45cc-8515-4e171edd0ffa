#!/bin/bash

# ECS Terminal Script
# This script provides an interactive-like experience for running commands in ECS containers

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_prompt() {
    echo -e "${BLUE}ecs>${NC} $1"
}

# Configuration
PROJECT_NAME="bopmaps"
ENVIRONMENT="prod"
AWS_REGION="us-east-1"

# Get network configuration from Terraform outputs
cd terraform
PRIVATE_SUBNET_ID=$(terraform output -json private_subnet_ids | jq -r '.[0]')
ECS_SECURITY_GROUP_ID=$(terraform output -raw ecs_security_group_id)
cd ..

print_status "ECS Terminal - Interactive Command Runner"
print_status "Type 'help' for available commands, 'exit' to quit"
echo ""

# Function to run a command in ECS
run_ecs_command() {
    local command="$1"
    local task_name="ecs-terminal-$(date +%s)"
    
    print_status "Running: $command"
    
    # Start the task
    local task_arn=$(aws ecs run-task \
        --cluster "$PROJECT_NAME-$ENVIRONMENT" \
        --task-definition "$PROJECT_NAME-$ENVIRONMENT-app" \
        --network-configuration "awsvpcConfiguration={subnets=[\"$PRIVATE_SUBNET_ID\"],securityGroups=[\"$ECS_SECURITY_GROUP_ID\"]}" \
        --overrides "{\"containerOverrides\":[{\"name\":\"app\",\"command\":[\"/bin/bash\",\"-c\",\"$command\"]}]}" \
        --region "$AWS_REGION" \
        --query 'tasks[0].taskArn' \
        --output text 2>/dev/null)
    
    if [ -z "$task_arn" ]; then
        print_error "Failed to start task"
        return 1
    fi
    
    print_status "Task started: $task_arn"
    
    # Wait for task to complete
    aws ecs wait tasks-stopped --cluster "$PROJECT_NAME-$ENVIRONMENT" --tasks "$task_arn" --region "$AWS_REGION" > /dev/null 2>&1
    
    # Get task logs
    print_status "Command output:"
    echo "----------------------------------------"
    aws logs tail "/ecs/$PROJECT_NAME-$ENVIRONMENT/app" --since 2m --follow=false 2>/dev/null | grep -E "(ecs/app|ERROR|WARNING|INFO)" || echo "No recent logs found"
    echo "----------------------------------------"
}

# Function to show help
show_help() {
    echo "Available commands:"
    echo "  help                    - Show this help"
    echo "  exit                    - Exit the terminal"
    echo "  shell                   - Start Django shell"
    echo "  createsuperuser         - Create a Django superuser"
    echo "  migrate                 - Run Django migrations"
    echo "  collectstatic           - Collect static files"
    echo "  check                   - Run Django system check"
    echo "  logs                    - Show recent logs"
    echo "  status                  - Show ECS service status"
    echo "  custom <command>        - Run a custom command"
    echo ""
    echo "Examples:"
    echo "  custom ls -la           - List files in container"
    echo "  custom python manage.py shell - Start Django shell"
    echo "  custom ps aux           - Show running processes"
}

# Function to show ECS status
show_status() {
    print_status "ECS Service Status:"
    aws ecs describe-services --cluster "$PROJECT_NAME-$ENVIRONMENT" --services "$PROJECT_NAME-$ENVIRONMENT-app" --query 'services[0].{Status:status,Running:runningCount,Desired:desiredCount}' --output table
}

# Main interactive loop
while true; do
    print_prompt ""
    read -r input
    
    case $input in
        "help")
            show_help
            ;;
        "exit"|"quit")
            print_status "Goodbye!"
            exit 0
            ;;
        "shell")
            run_ecs_command "python manage.py shell"
            ;;
        "createsuperuser")
            run_ecs_command "python manage.py createsuperuser --noinput --username admin --email <EMAIL>"
            ;;
        "migrate")
            run_ecs_command "python manage.py migrate"
            ;;
        "collectstatic")
            run_ecs_command "python manage.py collectstatic --noinput"
            ;;
        "check")
            run_ecs_command "python manage.py check"
            ;;
        "logs")
            print_status "Recent logs:"
            aws logs tail "/ecs/$PROJECT_NAME-$ENVIRONMENT/app" --since 5m --follow=false
            ;;
        "status")
            show_status
            ;;
        custom\ *)
            command="${input#custom }"
            run_ecs_command "$command"
            ;;
        "")
            continue
            ;;
        *)
            print_error "Unknown command: $input"
            print_status "Type 'help' for available commands"
            ;;
    esac
done 
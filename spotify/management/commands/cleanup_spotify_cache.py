from django.core.management.base import BaseCommand
from django.utils import timezone
from spotify.models import SpotifyCacheEntry, SpotifyUserCache, SpotifyRateLimit, SpotifyTokenUsage
from datetime import timedelta


class Command(BaseCommand):
    help = 'Clean up expired Spotify cache entries and old records'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Delete cache entries older than this many days (default: 7)'
        )
        parser.add_argument(
            '--rate-limit-hours',
            type=int,
            default=24,
            help='Delete rate limit records older than this many hours (default: 24)'
        )
        parser.add_argument(
            '--token-days',
            type=int,
            default=30,
            help='Delete inactive token records older than this many days (default: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
    
    def handle(self, *args, **options):
        dry_run = options['dry_run']
        days = options['days']
        rate_limit_hours = options['rate_limit_hours']
        token_days = options['token_days']
        
        now = timezone.now()
        
        # Clean up expired cache entries
        self.stdout.write(self.style.HTTP_INFO('Cleaning up expired Spotify cache entries...'))
        
        expired_entries = SpotifyCacheEntry.objects.filter(expires_at__lt=now)
        expired_count = expired_entries.count()
        
        if expired_count > 0:
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f'Would delete {expired_count} expired cache entries')
                )
            else:
                expired_entries.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {expired_count} expired cache entries')
                )
        else:
            self.stdout.write('No expired cache entries found')
        
        # Clean up expired user cache entries
        self.stdout.write(self.style.HTTP_INFO('Cleaning up expired user cache entries...'))
        
        expired_user_entries = SpotifyUserCache.objects.filter(expires_at__lt=now)
        expired_user_count = expired_user_entries.count()
        
        if expired_user_count > 0:
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f'Would delete {expired_user_count} expired user cache entries')
                )
            else:
                expired_user_entries.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {expired_user_count} expired user cache entries')
                )
        else:
            self.stdout.write('No expired user cache entries found')
        
        # Clean up old cache entries (even if not expired, if they're very old)
        old_threshold = now - timedelta(days=days)
        old_entries = SpotifyCacheEntry.objects.filter(created_at__lt=old_threshold)
        old_count = old_entries.count()
        
        if old_count > 0:
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f'Would delete {old_count} old cache entries (older than {days} days)')
                )
            else:
                old_entries.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {old_count} old cache entries (older than {days} days)')
                )
        else:
            self.stdout.write(f'No cache entries older than {days} days found')
        
        # Clean up old user cache entries
        old_user_entries = SpotifyUserCache.objects.filter(created_at__lt=old_threshold)
        old_user_count = old_user_entries.count()
        
        if old_user_count > 0:
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f'Would delete {old_user_count} old user cache entries (older than {days} days)')
                )
            else:
                old_user_entries.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {old_user_count} old user cache entries (older than {days} days)')
                )
        else:
            self.stdout.write(f'No user cache entries older than {days} days found')
        
        # Clean up old rate limit records
        self.stdout.write(self.style.HTTP_INFO('Cleaning up old rate limit records...'))
        
        rate_limit_threshold = now - timedelta(hours=rate_limit_hours)
        old_rate_limits = SpotifyRateLimit.objects.filter(window_start__lt=rate_limit_threshold)
        rate_limit_count = old_rate_limits.count()
        
        if rate_limit_count > 0:
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f'Would delete {rate_limit_count} old rate limit records (older than {rate_limit_hours} hours)')
                )
            else:
                old_rate_limits.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {rate_limit_count} old rate limit records (older than {rate_limit_hours} hours)')
                )
        else:
            self.stdout.write(f'No rate limit records older than {rate_limit_hours} hours found')
        
        # Clean up old token usage records
        self.stdout.write(self.style.HTTP_INFO('Cleaning up old token usage records...'))
        
        token_threshold = now - timedelta(days=token_days)
        old_tokens = SpotifyTokenUsage.objects.filter(
            last_used__lt=token_threshold,
            is_active=False
        )
        token_count = old_tokens.count()
        
        if token_count > 0:
            if dry_run:
                self.stdout.write(
                    self.style.WARNING(f'Would delete {token_count} old inactive token records (older than {token_days} days)')
                )
            else:
                old_tokens.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'Deleted {token_count} old inactive token records (older than {token_days} days)')
                )
        else:
            self.stdout.write(f'No inactive token records older than {token_days} days found')
        
        # Show cache statistics
        remaining_entries = SpotifyCacheEntry.objects.count()
        remaining_user_entries = SpotifyUserCache.objects.count()
        active_entries = SpotifyCacheEntry.objects.filter(expires_at__gt=now).count()
        active_user_entries = SpotifyUserCache.objects.filter(expires_at__gt=now).count()
        active_tokens = SpotifyTokenUsage.objects.filter(is_active=True).count()
        
        self.stdout.write(self.style.HTTP_INFO('\nSpotify Cache Statistics:'))
        self.stdout.write(f'Total cache entries: {remaining_entries}')
        self.stdout.write(f'Active cache entries: {active_entries}')
        self.stdout.write(f'Expired cache entries: {remaining_entries - active_entries}')
        self.stdout.write(f'Total user cache entries: {remaining_user_entries}')
        self.stdout.write(f'Active user cache entries: {active_user_entries}')
        self.stdout.write(f'Expired user cache entries: {remaining_user_entries - active_user_entries}')
        self.stdout.write(f'Active tokens: {active_tokens}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nThis was a dry run. No data was actually deleted.')
            )
            self.stdout.write('Run without --dry-run to perform the cleanup.')
        else:
            self.stdout.write(self.style.SUCCESS('\nCleanup completed successfully!')) 
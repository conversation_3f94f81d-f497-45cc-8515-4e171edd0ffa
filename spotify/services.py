import hashlib
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from urllib.parse import urlencode, urlparse

import requests
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.db import transaction
from django.db import models
from django.db.utils import IntegrityError

from .models import (
    SpotifyCacheEntry, 
    SpotifyApiUsage, 
    SpotifyRateLimit,
    SpotifyUserCache,
    SpotifyTokenUsage
)

logger = logging.getLogger(__name__)


class SpotifyRateLimitExceeded(Exception):
    """Exception raised when Spotify API rate limits are exceeded"""
    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = 60):
        self.message = message
        self.retry_after = retry_after
        super().__init__(self.message)


class SpotifyService:
    """Service for handling Spotify API requests with caching and rate limiting"""
    
    BASE_URL = 'https://api.spotify.com/v1'
    
    # Cache TTL settings for different endpoint types
    CACHE_TTL_SHORT = 60 * 15      # 15 minutes for user-specific data
    CACHE_TTL_MEDIUM = 60 * 60 * 2  # 2 hours for search results
    CACHE_TTL_LONG = 60 * 60 * 6    # 6 hours for track/artist details
    
    RATE_LIMIT_WINDOW = 60  # 1 minute
    MAX_REQUESTS_PER_WINDOW = 300  # 5 requests per second average
    REQUEST_TIMEOUT = 15
    
    # Endpoint configurations
    ENDPOINT_CONFIGS = {
        # User-specific endpoints (require user authentication)
        '/me/tracks': {'ttl': CACHE_TTL_SHORT, 'user_specific': True, 'auth_required': True},
        '/me/player/recently-played': {'ttl': CACHE_TTL_SHORT, 'user_specific': True, 'auth_required': True},
        '/me/top/tracks': {'ttl': CACHE_TTL_MEDIUM, 'user_specific': True, 'auth_required': True},
        '/me/top/artists': {'ttl': CACHE_TTL_MEDIUM, 'user_specific': True, 'auth_required': True},
        '/me/playlists': {'ttl': CACHE_TTL_SHORT, 'user_specific': True, 'auth_required': True},
        '/me/following': {'ttl': CACHE_TTL_SHORT, 'user_specific': True, 'auth_required': True},
        '/me': {'ttl': CACHE_TTL_MEDIUM, 'user_specific': True, 'auth_required': True},
        
        # Public endpoints (work with client credentials or no auth)
        '/search': {'ttl': CACHE_TTL_MEDIUM, 'user_specific': False, 'auth_required': False},
        '/tracks': {'ttl': CACHE_TTL_LONG, 'user_specific': False, 'auth_required': False},
        '/artists': {'ttl': CACHE_TTL_LONG, 'user_specific': False, 'auth_required': False},
        '/albums': {'ttl': CACHE_TTL_LONG, 'user_specific': False, 'auth_required': False},
        '/browse/categories': {'ttl': CACHE_TTL_LONG, 'user_specific': False, 'auth_required': False},
        '/browse/featured-playlists': {'ttl': CACHE_TTL_MEDIUM, 'user_specific': False, 'auth_required': False},
        '/browse/new-releases': {'ttl': CACHE_TTL_MEDIUM, 'user_specific': False, 'auth_required': False},
        '/recommendations': {'ttl': CACHE_TTL_MEDIUM, 'user_specific': False, 'auth_required': False},
        '/audio-features': {'ttl': CACHE_TTL_LONG, 'user_specific': False, 'auth_required': False},
        '/audio-analysis': {'ttl': CACHE_TTL_LONG, 'user_specific': False, 'auth_required': False},
        
        # Playlist endpoints (can work with client credentials for public playlists)
        '/playlists': {'ttl': CACHE_TTL_MEDIUM, 'user_specific': False, 'auth_required': False},
    }
    
    def __init__(self):
        self.client_id = getattr(settings, 'SPOTIFY_CLIENT_ID', '')
        self.client_secret = getattr(settings, 'SPOTIFY_CLIENT_SECRET', '')
        if not self.client_id:
            logger.warning("SPOTIFY_CLIENT_ID not configured in settings")
    
    def _get_endpoint_config(self, endpoint: str) -> Dict[str, Any]:
        """Get configuration for a specific endpoint"""
        # Find the most specific match
        for pattern, config in self.ENDPOINT_CONFIGS.items():
            if endpoint.startswith(pattern):
                return config
        
        # Default configuration
        return {
            'ttl': self.CACHE_TTL_MEDIUM,
            'user_specific': False,
            'auth_required': False
        }
    
    def _generate_cache_key(self, endpoint: str, params: Dict[str, Any], 
                           user_token: Optional[str] = None) -> str:
        """Generate a cache key for the request"""
        # Sort params for consistent hashing
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)
        
        # Include user token hash for user-specific endpoints
        if user_token and self._get_endpoint_config(endpoint)['user_specific']:
            user_hash = hashlib.sha256(user_token.encode()).hexdigest()[:16]
            cache_data = f"{endpoint}?{query_string}|user:{user_hash}"
        else:
            cache_data = f"{endpoint}?{query_string}"
        
        cache_hash = hashlib.sha1(cache_data.encode()).hexdigest()
        return f"spotify:{cache_hash}"
    
    def _generate_query_hash(self, params: Dict[str, Any]) -> str:
        """Generate a query hash for database storage"""
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)
        return hashlib.sha1(query_string.encode()).hexdigest()
    
    def _generate_user_hash(self, user_token: str) -> str:
        """Generate a privacy-preserving hash for user identification"""
        return hashlib.sha256(user_token.encode()).hexdigest()[:16]
    
    def _check_rate_limit(self, endpoint: str = '', user_ip: str = None) -> bool:
        """Check if we're within rate limits"""
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)
        
        # Check global rate limit
        try:
            global_limit, created = SpotifyRateLimit.objects.get_or_create(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None,
                defaults={'requests_made': 0}
            )
        except SpotifyRateLimit.MultipleObjectsReturned:
            # Handle the edge case where duplicates still exist
            logger.warning(f"Multiple rate limit records found for {endpoint}, cleaning up...")
            records = SpotifyRateLimit.objects.filter(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None
            ).order_by('id')
            global_limit = records.first()
            # Sum all requests and delete duplicates
            total_requests = sum(r.requests_made for r in records)
            global_limit.requests_made = total_requests
            global_limit.save()
            records.exclude(id=global_limit.id).delete()
        except IntegrityError:
            # Race condition - another request already created the record
            global_limit = SpotifyRateLimit.objects.get(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None
            )
        
        if global_limit.requests_made >= self.MAX_REQUESTS_PER_WINDOW:
            logger.warning(f"Global rate limit exceeded for endpoint {endpoint}")
            return False
        
        # Check per-IP rate limit if IP provided
        if user_ip:
            try:
                ip_limit, created = SpotifyRateLimit.objects.get_or_create(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip,
                    defaults={'requests_made': 0}
                )
            except SpotifyRateLimit.MultipleObjectsReturned:
                # Handle the edge case where duplicates still exist
                logger.warning(f"Multiple rate limit records found for IP {user_ip}, cleaning up...")
                records = SpotifyRateLimit.objects.filter(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip
                ).order_by('id')
                ip_limit = records.first()
                # Sum all requests and delete duplicates
                total_requests = sum(r.requests_made for r in records)
                ip_limit.requests_made = total_requests
                ip_limit.save()
                records.exclude(id=ip_limit.id).delete()
            except IntegrityError:
                # Race condition - another request already created the record
                ip_limit = SpotifyRateLimit.objects.get(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip
                )
            
            if ip_limit.requests_made >= 120:  # 2 requests per second per IP
                logger.warning(f"IP rate limit exceeded for {user_ip}")
                return False
        
        return True
    
    def _increment_rate_limit(self, endpoint: str = '', user_ip: str = None):
        """Increment rate limit counters"""
        now = timezone.now()
        window_start = now.replace(second=0, microsecond=0)
        
        # Increment global rate limit
        try:
            global_limit, created = SpotifyRateLimit.objects.get_or_create(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None,
                defaults={'requests_made': 1}
            )
            
            if not created:
                global_limit.requests_made = models.F('requests_made') + 1
                global_limit.save(update_fields=['requests_made'])
                
        except SpotifyRateLimit.MultipleObjectsReturned:
            # Handle the edge case where duplicates still exist
            logger.warning(f"Multiple rate limit records found while incrementing for {endpoint}")
            records = SpotifyRateLimit.objects.filter(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None
            ).order_by('id')
            global_limit = records.first()
            # Increment and clean up
            global_limit.requests_made += 1
            global_limit.save()
            records.exclude(id=global_limit.id).delete()
        except IntegrityError:
            # Race condition - use update instead
            SpotifyRateLimit.objects.filter(
                window_start=window_start,
                endpoint=endpoint,
                user_ip=None
            ).update(requests_made=models.F('requests_made') + 1)
        
        # Increment per-IP rate limit if IP provided
        if user_ip:
            try:
                ip_limit, created = SpotifyRateLimit.objects.get_or_create(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip,
                    defaults={'requests_made': 1}
                )
                
                if not created:
                    ip_limit.requests_made = models.F('requests_made') + 1
                    ip_limit.save(update_fields=['requests_made'])
                    
            except SpotifyRateLimit.MultipleObjectsReturned:
                # Handle the edge case where duplicates still exist
                logger.warning(f"Multiple rate limit records found while incrementing for IP {user_ip}")
                records = SpotifyRateLimit.objects.filter(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip
                ).order_by('id')
                ip_limit = records.first()
                # Increment and clean up
                ip_limit.requests_made += 1
                ip_limit.save()
                records.exclude(id=ip_limit.id).delete()
            except IntegrityError:
                # Race condition - use update instead
                SpotifyRateLimit.objects.filter(
                    window_start=window_start,
                    endpoint='',
                    user_ip=user_ip
                ).update(requests_made=models.F('requests_made') + 1)
    
    def _get_from_cache(self, cache_key: str, endpoint: str, 
                       user_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get data from cache (Redis first, then database)"""
        config = self._get_endpoint_config(endpoint)
        
        # Try Redis first
        cached_data = cache.get(cache_key)
        if cached_data:
            self._update_usage_stats(endpoint, cache_hit=True)
            logger.info(f"Cache hit (Redis) for {cache_key}")
            return cached_data
        
        # Try appropriate database cache
        try:
            if config['user_specific'] and user_token:
                user_hash = self._generate_user_hash(user_token)
                cache_entry = SpotifyUserCache.objects.get(
                    cache_key=cache_key,
                    user_hash=user_hash,
                    expires_at__gt=timezone.now()
                )
            else:
                cache_entry = SpotifyCacheEntry.objects.get(
                    cache_key=cache_key,
                    expires_at__gt=timezone.now()
                )
            
            cache_entry.increment_hit_count()
            
            # Store back in Redis for faster access
            cache.set(cache_key, cache_entry.response_data, config['ttl'])
            
            self._update_usage_stats(endpoint, cache_hit=True)
            logger.info(f"Cache hit (Database) for {cache_key}")
            return cache_entry.response_data
            
        except (SpotifyCacheEntry.DoesNotExist, SpotifyUserCache.DoesNotExist):
            return None
    
    def _store_in_cache(self, cache_key: str, endpoint: str, query_hash: str, 
                       data: Dict[str, Any], user_token: Optional[str] = None):
        """Store data in both Redis and database cache"""
        config = self._get_endpoint_config(endpoint)
        expires_at = timezone.now() + timedelta(seconds=config['ttl'])
        
        # Store in Redis
        cache.set(cache_key, data, config['ttl'])
        
        # Store in appropriate database cache
        try:
            with transaction.atomic():
                if config['user_specific'] and user_token:
                    user_hash = self._generate_user_hash(user_token)
                    SpotifyUserCache.objects.update_or_create(
                        cache_key=cache_key,
                        defaults={
                            'endpoint': endpoint,
                            'user_hash': user_hash,
                            'response_data': data,
                            'expires_at': expires_at,
                            'hit_count': 0,
                        }
                    )
                else:
                    SpotifyCacheEntry.objects.update_or_create(
                        cache_key=cache_key,
                        defaults={
                            'endpoint': endpoint,
                            'query_hash': query_hash,
                            'response_data': data,
                            'expires_at': expires_at,
                            'user_specific': config['user_specific'],
                            'hit_count': 0,
                        }
                    )
            logger.info(f"Stored in cache: {cache_key}")
        except Exception as e:
            logger.error(f"Error storing cache entry: {e}")
    
    def _update_usage_stats(self, endpoint: str, cache_hit: bool = False, 
                           api_call: bool = False, response_time: float = 0.0):
        """Update usage statistics"""
        today = timezone.now().date()
        
        try:
            usage, created = SpotifyApiUsage.objects.get_or_create(
                date=today,
                endpoint=endpoint,
                defaults={
                    'total_requests': 0,
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'api_calls_made': 0,
                    'average_response_time': 0.0,
                    'unique_users': 0,
                }
            )
            
            usage.total_requests += 1
            
            if cache_hit:
                usage.cache_hits += 1
            else:
                usage.cache_misses += 1
            
            if api_call:
                usage.api_calls_made += 1
                # Update average response time
                if usage.api_calls_made == 1:
                    usage.average_response_time = response_time
                else:
                    usage.average_response_time = (
                        (usage.average_response_time * (usage.api_calls_made - 1) + response_time) 
                        / usage.api_calls_made
                    )
            
            usage.save()
            
        except Exception as e:
            logger.error(f"Error updating usage stats: {e}")
    
    def _track_token_usage(self, access_token: str, success: bool = True):
        """Track token usage for monitoring"""
        try:
            token_hash = hashlib.sha256(access_token.encode()).hexdigest()[:16]
            token_usage, created = SpotifyTokenUsage.objects.get_or_create(
                token_hash=token_hash,
                defaults={
                    'request_count': 0,
                    'successful_requests': 0,
                    'failed_requests': 0,
                    'is_active': True,
                }
            )
            token_usage.record_request(success)
        except Exception as e:
            logger.error(f"Error tracking token usage: {e}")
    
    def _make_api_request(self, endpoint: str, access_token: Optional[str] = None,
                         params: Dict[str, Any] = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Make request to Spotify API with enhanced authentication handling"""
        config = self._get_endpoint_config(endpoint)
        
        # Determine authentication strategy
        auth_token = None
        auth_type = None
        
        if access_token:
            # User provided an access token
            auth_token = access_token
            auth_type = "user_token"
            logger.info(f"Using user access token for {endpoint}")
        elif config['auth_required']:
            # Endpoint requires auth but no user token provided - try client credentials
            client_token = self._get_client_credentials_token()
            if client_token:
                auth_token = client_token
                auth_type = "client_credentials"
                logger.info(f"Using client credentials token for {endpoint}")
            else:
                logger.error(f"Endpoint {endpoint} requires authentication but no valid tokens available")
                return None
        elif not config['auth_required']:
            # Public endpoint - we can still use client credentials for better rate limits
            client_token = self._get_client_credentials_token()
            if client_token:
                auth_token = client_token
                auth_type = "client_credentials"
                logger.info(f"Using client credentials for public endpoint {endpoint} (better rate limits)")
            else:
                logger.info(f"Making unauthenticated request to public endpoint {endpoint}")
        
        # Check rate limit
        if not self._check_rate_limit(endpoint, user_ip):
            logger.warning("Rate limit exceeded, skipping API request")
            raise SpotifyRateLimitExceeded("Rate limit exceeded", retry_after=60)
        
        # Prepare request
        url = f"{self.BASE_URL}{endpoint}"
        headers = {'Content-Type': 'application/json'}
        
        if auth_token:
            headers['Authorization'] = f'Bearer {auth_token}'
        
        start_time = time.time()
        
        try:
            logger.debug(f"Making {auth_type or 'unauthenticated'} request to {url} with params: {params}")
            
            response = requests.get(
                url,
                headers=headers,
                params=params,
                timeout=self.REQUEST_TIMEOUT
            )
            
            response_time = time.time() - start_time
            
            # Increment rate limit counter
            self._increment_rate_limit(endpoint, user_ip)
            
            # Track token usage
            if auth_token:
                self._track_token_usage(auth_token, response.status_code < 400)
            
            if response.status_code == 200:
                data = response.json()
                self._update_usage_stats(endpoint, api_call=True, response_time=response_time)
                logger.info(f"Successful API request for {endpoint} using {auth_type or 'no auth'}")
                return data
            
            elif response.status_code == 401:
                logger.warning(f"Unauthorized request to {endpoint} - token may be expired (auth_type: {auth_type})")
                if auth_token:
                    self._track_token_usage(auth_token, False)
                    
                # If user token failed and we have client credentials, try fallback
                if auth_type == "user_token" and not config.get('user_specific', False):
                    logger.info(f"User token failed for {endpoint}, attempting client credentials fallback")
                    return self._make_api_request_with_fallback(endpoint, params, user_ip)
                return None
            
            elif response.status_code == 403:
                logger.warning(f"Forbidden request to {endpoint} - insufficient permissions (auth_type: {auth_type})")
                return None
            
            elif response.status_code == 404:
                logger.warning(f"Not found request to {endpoint} (auth_type: {auth_type})")
                
                # For playlist endpoints, 404 might mean private playlist - try client credentials fallback
                if (auth_type == "user_token" and 
                    endpoint.startswith('/playlists/') and 
                    not config.get('user_specific', False)):
                    logger.info(f"Playlist not found with user token for {endpoint}, attempting client credentials fallback")
                    return self._make_api_request_with_fallback(endpoint, params, user_ip)
                
                return {
                    'error': {
                        'status': 404,
                        'message': 'The requested resource was not found'
                    }
                }
            
            elif response.status_code == 429:
                logger.warning(f"Rate limited by Spotify API for {endpoint}")
                # Parse retry-after header if available
                retry_after = int(response.headers.get('Retry-After', 60))
                raise SpotifyRateLimitExceeded("Rate limited by Spotify API", retry_after=retry_after)
            
            else:
                logger.error(f"HTTP error {response.status_code} for {endpoint}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error(f"Timeout making request to Spotify API for endpoint {endpoint}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Error making request to Spotify API for {endpoint}: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response from {endpoint}: {e}")
            return None
    
    def _get_client_credentials_token(self) -> Optional[str]:
        """Get client credentials token for non-user endpoints with enhanced logging and error handling"""
        if not self.client_id or not self.client_secret:
            logger.warning("Spotify client credentials not configured - set SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET")
            return None
        
        # Check cache first
        cached_token = cache.get('spotify_client_token')
        if cached_token:
            logger.debug("Using cached client credentials token")
            return cached_token
        
        logger.info("Requesting new client credentials token from Spotify")
        
        try:
            # Request new token using client credentials flow
            auth_url = 'https://accounts.spotify.com/api/token'
            
            # Prepare credentials for Basic auth
            import base64
            credentials = base64.b64encode(f"{self.client_id}:{self.client_secret}".encode()).decode()
            
            headers = {
                'Authorization': f'Basic {credentials}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'grant_type': 'client_credentials'
            }
            
            logger.debug(f"Making client credentials request to {auth_url}")
            
            response = requests.post(auth_url, headers=headers, data=data, timeout=10)
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data['access_token']
                expires_in = token_data.get('expires_in', 3600)
                
                # Cache with some buffer time (60 seconds before expiry)
                cache_timeout = max(expires_in - 60, 300)  # Minimum 5 minutes
                cache.set('spotify_client_token', access_token, cache_timeout)
                
                logger.info(f"Successfully obtained client credentials token (expires in {expires_in}s)")
                return access_token
            
            elif response.status_code == 400:
                logger.error(f"Bad request for client credentials: {response.text}")
                # This usually means invalid client_id/secret
                return None
            
            elif response.status_code == 401:
                logger.error("Unauthorized - invalid Spotify client credentials")
                return None
                
            else:
                logger.error(f"Unexpected response getting client credentials token: {response.status_code} - {response.text}")
                return None
            
        except requests.exceptions.Timeout:
            logger.error("Timeout requesting client credentials token from Spotify")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error requesting client credentials token: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing client credentials token response: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting client credentials token: {e}")
            return None
    
    def _make_api_request_with_fallback(self, endpoint: str, params: Dict[str, Any] = None, 
                                       user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Make API request with client credentials fallback"""
        config = self._get_endpoint_config(endpoint)
        
        # Only attempt fallback for non-user-specific endpoints
        if config.get('user_specific', False):
            logger.warning(f"Cannot use client credentials fallback for user-specific endpoint {endpoint}")
            return None
        
        client_token = self._get_client_credentials_token()
        if not client_token:
            logger.error(f"Client credentials fallback failed - no client token available for {endpoint}")
            return None
        
        logger.info(f"Attempting client credentials fallback for {endpoint}")
        
        # Make request with client token (without user context)
        url = f"{self.BASE_URL}{endpoint}"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {client_token}'
        }
        
        try:
            response = requests.get(
                url,
                headers=headers,
                params=params,
                timeout=self.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Client credentials fallback successful for {endpoint}")
                return data
            else:
                logger.warning(f"Client credentials fallback failed for {endpoint}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error in client credentials fallback for {endpoint}: {e}")
            return None
    
    def get_data(self, endpoint: str, access_token: Optional[str] = None,
                params: Dict[str, Any] = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get data from Spotify API with caching"""
        if params is None:
            params = {}
        
        # Generate cache key
        cache_key = self._generate_cache_key(endpoint, params, access_token)
        query_hash = self._generate_query_hash(params)
        
        logger.info(f"Processing request for endpoint: {endpoint}")
        
        # Try to get from cache first
        cached_data = self._get_from_cache(cache_key, endpoint, access_token)
        if cached_data:
            return cached_data
        
        # Make API request
        try:
            data = self._make_api_request(endpoint, access_token, params, user_ip)
            if data:
                # Store in cache
                self._store_in_cache(cache_key, endpoint, query_hash, data, access_token)
                self._update_usage_stats(endpoint, cache_hit=False)
                return data
            
            return None
        except SpotifyRateLimitExceeded as e:
            # Re-raise the rate limit exception to be handled by the view
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in get_data: {e}")
            return None
    
    def search(self, query: str, search_type: str = 'track', access_token: Optional[str] = None,
               limit: int = 20, offset: int = 0, market: str = None,
               user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Search for tracks, artists, albums, or playlists"""
        # Validate search type
        valid_types = ['track', 'artist', 'album', 'playlist']
        if search_type not in valid_types:
            logger.warning(f"Invalid search type: {search_type}. Using 'track' as default.")
            search_type = 'track'
        
        params = {
            'q': query,
            'type': search_type,
            'limit': str(limit),
            'offset': str(offset),
        }
        if market:
            params['market'] = market
        
        return self.get_data('/search', access_token, params, user_ip)
    
    def search_tracks(self, query: str, access_token: Optional[str] = None,
                     limit: int = 20, offset: int = 0, market: str = None,
                     user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Search for tracks (backward compatibility)"""
        return self.search(query, 'track', access_token, limit, offset, market, user_ip)
    
    def search_playlists(self, query: str, access_token: Optional[str] = None,
                        limit: int = 20, offset: int = 0, market: str = None,
                        user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Search for playlists"""
        return self.search(query, 'playlist', access_token, limit, offset, market, user_ip)
    
    def search_artists(self, query: str, access_token: Optional[str] = None,
                      limit: int = 20, offset: int = 0, market: str = None,
                      user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Search for artists"""
        return self.search(query, 'artist', access_token, limit, offset, market, user_ip)
    
    def search_albums(self, query: str, access_token: Optional[str] = None,
                     limit: int = 20, offset: int = 0, market: str = None,
                     user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Search for albums"""
        return self.search(query, 'album', access_token, limit, offset, market, user_ip)
    
    def get_user_saved_tracks(self, access_token: str, limit: int = 50, offset: int = 0,
                             market: str = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get user's saved tracks"""
        params = {
            'limit': str(limit),
            'offset': str(offset),
        }
        if market:
            params['market'] = market
        
        return self.get_data('/me/tracks', access_token, params, user_ip)
    
    def get_user_recently_played(self, access_token: str, limit: int = 20,
                                user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get user's recently played tracks"""
        params = {'limit': str(limit)}
        return self.get_data('/me/player/recently-played', access_token, params, user_ip)
    
    def get_user_top_tracks(self, access_token: str, time_range: str = 'medium_term',
                           limit: int = 20, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get user's top tracks"""
        params = {
            'time_range': time_range,
            'limit': str(limit),
        }
        return self.get_data('/me/top/tracks', access_token, params, user_ip)
    
    def get_user_top_artists(self, access_token: str, time_range: str = 'medium_term',
                            limit: int = 20, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get user's top artists"""
        params = {
            'time_range': time_range,
            'limit': str(limit),
        }
        return self.get_data('/me/top/artists', access_token, params, user_ip)
    
    def get_user_playlists(self, access_token: str, limit: int = 20, offset: int = 0,
                          user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get user's playlists"""
        params = {
            'limit': str(limit),
            'offset': str(offset),
        }
        return self.get_data('/me/playlists', access_token, params, user_ip)
    
    def get_playlist_tracks(self, playlist_id: str, access_token: Optional[str] = None,
                           limit: int = 50, offset: int = 0, market: str = None,
                           user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get playlist tracks"""
        params = {
            'limit': str(limit),
            'offset': str(offset),
        }
        if market and market.lower() != 'from_token':
            params['market'] = market
        
        endpoint = f'/playlists/{playlist_id}/tracks'
        return self.get_data(endpoint, access_token, params, user_ip)
    
    def get_track_details(self, track_id: str, access_token: Optional[str] = None,
                         market: str = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get track details"""
        params = {}
        if market:
            params['market'] = market
        
        endpoint = f'/tracks/{track_id}'
        return self.get_data(endpoint, access_token, params, user_ip)
    
    def get_multiple_tracks(self, track_ids: List[str], access_token: Optional[str] = None,
                           market: str = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get multiple tracks"""
        params = {'ids': ','.join(track_ids)}
        if market:
            params['market'] = market
        
        return self.get_data('/tracks', access_token, params, user_ip)
    
    def get_artist_details(self, artist_id: str, access_token: Optional[str] = None,
                          user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get artist details"""
        endpoint = f'/artists/{artist_id}'
        return self.get_data(endpoint, access_token, {}, user_ip)
    
    def get_multiple_artists(self, artist_ids: List[str], access_token: Optional[str] = None,
                            user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get multiple artists"""
        params = {'ids': ','.join(artist_ids)}
        return self.get_data('/artists', access_token, params, user_ip)
    
    def get_artist_albums(self, artist_id: str, access_token: Optional[str] = None,
                          include_groups: str = 'album,single,appears_on', limit: int = 20,
                          offset: int = 0, market: str = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get artist's albums"""
        params = {
            'include_groups': include_groups,
            'limit': str(limit),
            'offset': str(offset),
        }
        if market:
            params['market'] = market
        
        endpoint = f'/artists/{artist_id}/albums'
        return self.get_data(endpoint, access_token, params, user_ip)
    
    def get_album_details(self, album_id: str, access_token: Optional[str] = None,
                         market: str = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get album details"""
        params = {}
        if market:
            params['market'] = market
        
        endpoint = f'/albums/{album_id}'
        return self.get_data(endpoint, access_token, params, user_ip)
    
    def get_multiple_albums(self, album_ids: List[str], access_token: Optional[str] = None,
                           market: str = None, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get multiple albums"""
        params = {'ids': ','.join(album_ids)}
        if market:
            params['market'] = market
        
        return self.get_data('/albums', access_token, params, user_ip)
    
    def get_user_profile(self, access_token: str, user_ip: str = None) -> Optional[Dict[str, Any]]:
        """Get user profile"""
        return self.get_data('/me', access_token, {}, user_ip)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        today = timezone.now().date()
        
        # Get today's stats
        stats = SpotifyApiUsage.objects.filter(date=today).aggregate(
            total_requests=models.Sum('total_requests'),
            total_cache_hits=models.Sum('cache_hits'),
            total_cache_misses=models.Sum('cache_misses'),
            total_api_calls=models.Sum('api_calls_made'),
            unique_users=models.Sum('unique_users'),
        )
        
        # Get cache entry counts
        total_entries = SpotifyCacheEntry.objects.count()
        user_entries = SpotifyUserCache.objects.count()
        expired_entries = SpotifyCacheEntry.objects.filter(
            expires_at__lt=timezone.now()
        ).count()
        expired_user_entries = SpotifyUserCache.objects.filter(
            expires_at__lt=timezone.now()
        ).count()
        
        # Get token stats
        active_tokens = SpotifyTokenUsage.objects.filter(is_active=True).count()
        
        return {
            'today_stats': stats,
            'total_cache_entries': total_entries,
            'total_user_cache_entries': user_entries,
            'expired_entries': expired_entries,
            'expired_user_entries': expired_user_entries,
            'active_entries': total_entries - expired_entries,
            'active_user_entries': user_entries - expired_user_entries,
            'active_tokens': active_tokens,
        }
    
    def cleanup_expired_cache(self):
        """Clean up expired cache entries"""
        expired_count = SpotifyCacheEntry.objects.filter(
            expires_at__lt=timezone.now()
        ).delete()[0]
        
        expired_user_count = SpotifyUserCache.objects.filter(
            expires_at__lt=timezone.now()
        ).delete()[0]
        
        logger.info(f"Cleaned up {expired_count} expired cache entries and {expired_user_count} user cache entries")
        return expired_count + expired_user_count

    def get_trending_tracks(self, limit: int = 50, offset: int = 0, market: str = 'US') -> List[Dict]:
        """Get trending tracks from Spotify Global Top 50"""
        try:
            # Use the Global Top 50 playlist
            global_top_50_id = '37i9dQZEVXbMDoHDwVN2tF'

            response = self.get_playlist_tracks(
                playlist_id=global_top_50_id,
                limit=limit,
                offset=offset,
                market=market
            )

            tracks = []
            if response and 'items' in response:
                for item in response['items']:
                    track = item.get('track')
                    if track:
                        formatted_track = self._format_track_data(track)
                        if formatted_track:
                            tracks.append(formatted_track)

            return tracks

        except Exception as e:
            logger.error(f"Error getting trending tracks: {str(e)}")
            return []

    def get_tracks_by_genre(self, genre: str, limit: int = 50, offset: int = 0, market: str = 'US') -> List[Dict]:
        """Get tracks by genre using search"""
        try:
            # Search for tracks by genre
            search_query = f'genre:"{genre}"'

            params = {
                'q': search_query,
                'type': 'track',
                'limit': str(limit),
                'offset': str(offset),
                'market': market
            }

            response = self.get_data('/search', params=params)

            tracks = []
            if response and 'tracks' in response and 'items' in response['tracks']:
                for track in response['tracks']['items']:
                    formatted_track = self._format_track_data(track)
                    if formatted_track:
                        tracks.append(formatted_track)

            return tracks

        except Exception as e:
            logger.error(f"Error getting tracks by genre {genre}: {str(e)}")
            return []

    def _format_track_data(self, track: Dict) -> Optional[Dict]:
        """Format track data into consistent structure"""
        try:
            if not track or not track.get('id'):
                return None

            artists = track.get('artists', [])
            artist_names = [artist.get('name', '') for artist in artists if artist.get('name')]

            return {
                'title': track.get('name', ''),
                'artist': ', '.join(artist_names) if artist_names else 'Unknown Artist',
                'spotify_id': track.get('id'),
                'preview_url': track.get('preview_url'),
                'popularity': track.get('popularity', 0),
                'duration_ms': track.get('duration_ms', 0),
                'explicit': track.get('explicit', False),
                'genre': 'pop',  # Default, can be enhanced with audio features
                'mood': 'happy'  # Default, can be enhanced with audio features
            }

        except Exception as e:
            logger.error(f"Error formatting track data: {str(e)}")
            return None
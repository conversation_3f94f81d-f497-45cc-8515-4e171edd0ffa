import requests
import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from typing import List, Dict, Any, Optional
from .models import OneSignalPlayerID, Notification, NotificationBatch

logger = logging.getLogger(__name__)
User = get_user_model()


class OneSignalService:
    """
    Service for sending push notifications via OneSignal API
    """
    
    def __init__(self):
        self.app_id = getattr(settings, 'ONESIGNAL_APP_ID', None)
        self.api_key = getattr(settings, 'ONESIGNAL_API_KEY', None)
        self.api_url = getattr(settings, 'ONESIGNAL_API_URL', 'https://onesignal.com/api/v1/notifications')
        
        if not self.app_id or not self.api_key:
            logger.warning("OneSignal credentials not configured properly")
    
    def send_notification(
        self,
        title: str,
        message: str,
        user_ids: Optional[List[str]] = None,
        player_ids: Optional[List[str]] = None,
        data: Optional[Dict[str, Any]] = None,
        image_url: Optional[str] = None,
        action_buttons: Optional[List[Dict]] = None,
        priority: str = 'normal'
    ) -> Dict[str, Any]:
        """
        Send a push notification via OneSignal
        
        Args:
            title: Notification title
            message: Notification message
            user_ids: List of user IDs (external_user_ids)
            player_ids: List of OneSignal player IDs
            data: Additional data payload
            image_url: Optional image URL
            action_buttons: Optional action buttons
            priority: Notification priority ('normal' or 'high')
        
        Returns:
            Dictionary with success status and response data
        """
        if not self.app_id or not self.api_key:
            return {
                'success': False,
                'error': 'OneSignal not configured'
            }
        
        headers = {
            'Authorization': f'Basic {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'app_id': self.app_id,
            'headings': {'en': title},
            'contents': {'en': message},
            'data': data or {},
            'priority': 10 if priority == 'high' else 5,
        }
        
        # Only add android_channel_id if configured
        android_channel_id = getattr(settings, 'ONESIGNAL_ANDROID_CHANNEL_ID', None)
        if android_channel_id:
            payload['android_channel_id'] = android_channel_id
        
        # Set target audience
        if user_ids:
            payload['include_external_user_ids'] = user_ids
        elif player_ids:
            payload['include_player_ids'] = player_ids
        else:
            return {
                'success': False,
                'error': 'No target users specified'
            }
        
        # Add image if provided
        if image_url:
            payload['big_picture'] = image_url
            payload['large_icon'] = image_url
            payload['ios_attachments'] = {'image': image_url}
        
        # Add action buttons if provided
        if action_buttons:
            payload['buttons'] = action_buttons
        
        try:
            response = requests.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"OneSignal notification sent successfully: {response_data}")
                return {
                    'success': True,
                    'response': response_data,
                    'notification_id': response_data.get('id')
                }
            else:
                error_msg = f"OneSignal API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            error_msg = f"OneSignal request failed: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def send_to_user(
        self,
        user: User,
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        image_url: Optional[str] = None,
        action_buttons: Optional[List[Dict]] = None,
        priority: str = 'normal'
    ) -> Dict[str, Any]:
        """
        Send notification to a specific user using user ID directly
        """
        # Use user ID directly as external_user_id in OneSignal
        # This bypasses the need for OneSignalPlayerID registration
        user_id = str(user.id)

        return self.send_notification_to_external_user(
            title=title,
            message=message,
            external_user_id=user_id,
            data=data,
            image_url=image_url,
            action_buttons=action_buttons,
            priority=priority
        )

    def send_notification_to_external_user(
        self,
        title: str,
        message: str,
        external_user_id: str,
        data: Optional[Dict[str, Any]] = None,
        image_url: Optional[str] = None,
        action_buttons: Optional[List[Dict]] = None,
        priority: str = 'normal'
    ) -> Dict[str, Any]:
        """
        Send notification to user using external user ID (user.id)
        """
        if not self.app_id or not self.api_key:
            return {
                'success': False,
                'error': 'OneSignal not configured'
            }

        try:
            # Build notification payload for external user ID
            payload = {
                'app_id': self.app_id,
                'include_external_user_ids': [external_user_id],
                'headings': {'en': title},
                'contents': {'en': message},
                'priority': 10 if priority == 'high' else 5,
            }

            # Add optional data
            if data:
                payload['data'] = data

            # Add image if provided
            if image_url:
                payload['big_picture'] = image_url
                payload['large_icon'] = image_url

            # Add action buttons if provided
            if action_buttons:
                payload['buttons'] = action_buttons

            # Send the notification
            headers = {
                'Authorization': f'Basic {self.api_key}',
                'Content-Type': 'application/json'
            }

            response = requests.post(self.api_url, json=payload, headers=headers)

            if response.status_code == 200:
                response_data = response.json()
                return {
                    'success': True,
                    'notification_id': response_data.get('id'),
                    'recipients': response_data.get('recipients', 0),
                    'response': response_data
                }
            else:
                logger.error(f"OneSignal API error: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f'OneSignal API error: {response.status_code} - {response.text}'
                }

        except Exception as e:
            logger.error(f"Failed to send OneSignal notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def send_to_users(
        self,
        users: List[User],
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        image_url: Optional[str] = None,
        action_buttons: Optional[List[Dict]] = None,
        priority: str = 'normal'
    ) -> Dict[str, Any]:
        """
        Send notification to multiple users
        """
        user_ids = [str(user.id) for user in users]
        
        return self.send_notification(
            title=title,
            message=message,
            user_ids=user_ids,
            data=data,
            image_url=image_url,
            action_buttons=action_buttons,
            priority=priority
        )
    
    def send_to_segment(
        self,
        segment_name: str,
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        image_url: Optional[str] = None,
        action_buttons: Optional[List[Dict]] = None,
        priority: str = 'normal'
    ) -> Dict[str, Any]:
        """
        Send notification to a OneSignal segment
        """
        if not self.app_id or not self.api_key:
            return {
                'success': False,
                'error': 'OneSignal not configured'
            }
        
        headers = {
            'Authorization': f'Basic {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'app_id': self.app_id,
            'headings': {'en': title},
            'contents': {'en': message},
            'data': data or {},
            'included_segments': [segment_name],
            'android_channel_id': 'bopmaps_notifications',
            'priority': 10 if priority == 'high' else 5,
        }
        
        # Add image if provided
        if image_url:
            payload['big_picture'] = image_url
            payload['large_icon'] = image_url
            payload['ios_attachments'] = {'image': image_url}
        
        # Add action buttons if provided
        if action_buttons:
            payload['buttons'] = action_buttons
        
        try:
            response = requests.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"OneSignal segment notification sent successfully: {response_data}")
                return {
                    'success': True,
                    'response': response_data,
                    'notification_id': response_data.get('id')
                }
            else:
                error_msg = f"OneSignal API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            error_msg = f"OneSignal request failed: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def register_player(
        self,
        user: User,
        player_id: str,
        platform: str = 'web'
    ) -> bool:
        """
        Register a OneSignal player ID for a user
        """
        try:
            player, created = OneSignalPlayerID.objects.get_or_create(
                user=user,
                player_id=player_id,
                defaults={'platform': platform}
            )
            
            if not created:
                # Update existing player
                player.is_active = True
                player.platform = platform
                player.save()
            
            logger.info(f"Registered OneSignal player {player_id} for user {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register OneSignal player: {str(e)}")
            return False
    
    def unregister_player(
        self,
        user: User,
        player_id: str
    ) -> bool:
        """
        Unregister a OneSignal player ID for a user
        """
        try:
            OneSignalPlayerID.objects.filter(
                user=user,
                player_id=player_id
            ).update(is_active=False)
            
            logger.info(f"Unregistered OneSignal player {player_id} for user {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister OneSignal player: {str(e)}")
            return False
    
    def get_notification_status(self, notification_id: str) -> Dict[str, Any]:
        """
        Get the status of a sent notification
        """
        if not self.api_key:
            return {
                'success': False,
                'error': 'OneSignal not configured'
            }
        
        headers = {
            'Authorization': f'Basic {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.get(
                f'https://onesignal.com/api/v1/notifications/{notification_id}?app_id={self.app_id}',
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'data': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f"API error: {response.status_code} - {response.text}"
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f"Request failed: {str(e)}"
            }


# Global instance
onesignal_service = OneSignalService() 
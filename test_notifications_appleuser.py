#!/usr/bin/env python
"""
Test script to send all notification types to appleuser for testing on phone
"""
import os
import sys
import django
import time
import requests
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bopmaps.settings')
django.setup()

from django.contrib.auth import get_user_model
from notifications.models import NotificationType, NotificationCategory, NotificationPriority
from notifications.manager import notification_manager
from notifications.services import onesignal_service

User = get_user_model()

def test_all_notifications():
    """Test all notification types for appleuser"""

    # Get the appleuser by ID
    try:
        user = User.objects.get(id=300)  # Use the known ID
        print(f"✅ Found user: {user.username} (ID: {user.id})")
        print(f"📧 Email: {user.email}")
    except User.DoesNotExist:
        print("❌ User with ID 300 not found!")
        return

    # Check if user has OneSignal player ID registered
    from notifications.models import OneSignalPlayerID
    player_ids = OneSignalPlayerID.objects.filter(user=user, is_active=True)
    print(f"📱 OneSignal Player IDs: {player_ids.count()}")
    for player in player_ids:
        print(f"   - {player.platform}: {player.player_id[:20]}...")

    print("\n🚀 Starting notification tests (will create notifications in database)...\n")
    
    # Define test notifications for each type
    test_notifications = [
        # Map & Pin related
        {
            'type': NotificationType.PIN_LIKE,
            'category': NotificationCategory.MAP,
            'title': '❤️ Pin Liked!',
            'message': 'Someone loved your music pin "Bohemian Rhapsody"!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.PIN_COMMENT,
            'category': NotificationCategory.MAP,
            'title': '💬 New Comment',
            'message': 'John commented on your pin: "Great song choice!"',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.PIN_TRENDING,
            'category': NotificationCategory.MAP,
            'title': '🔥 Pin Trending!',
            'message': 'Your pin "Blinding Lights" is trending in your area!',
            'priority': NotificationPriority.HIGH
        },
        {
            'type': NotificationType.FRIEND_NEARBY,
            'category': NotificationCategory.MAP,
            'title': '👋 Friend Nearby',
            'message': 'Sarah is nearby and just dropped a new pin!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.PIN_MILESTONE,
            'category': NotificationCategory.MAP,
            'title': '🎯 Pin Milestone',
            'message': 'Your pin reached 100 likes! Keep sharing great music!',
            'priority': NotificationPriority.HIGH
        },
        
        # Social & Friends
        {
            'type': NotificationType.FRIEND_REQUEST,
            'category': NotificationCategory.SOCIAL,
            'title': '👥 Friend Request',
            'message': 'Alex wants to be your friend on BOP Maps!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.FRIEND_ACCEPTED,
            'category': NotificationCategory.SOCIAL,
            'title': '🎉 Friend Accepted',
            'message': 'Emma accepted your friend request!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.MUSIC_CHAT,
            'category': NotificationCategory.SOCIAL,
            'title': '🎵 Music Chat',
            'message': 'Mike sent you a message about "Starboy"',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.ACTIVITY_DIGEST,
            'category': NotificationCategory.SOCIAL,
            'title': '📊 Activity Digest',
            'message': 'Your friends dropped 5 new pins this week!',
            'priority': NotificationPriority.LOW
        },
        {
            'type': NotificationType.LIVE_LISTENING,
            'category': NotificationCategory.SOCIAL,
            'title': '🎧 Live Listening',
            'message': '3 friends are listening to music nearby!',
            'priority': NotificationPriority.MEDIUM
        },
        
        # Music related
        {
            'type': NotificationType.WEEKLY_RECOMMENDATION,
            'category': NotificationCategory.MUSIC,
            'title': '🎶 Weekly Mix',
            'message': 'Your weekly music recommendations are ready!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.FAVORITE_ARTIST_UPDATE,
            'category': NotificationCategory.MUSIC,
            'title': '🎤 Artist Update',
            'message': 'The Weeknd just released a new album!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.NEW_RELEASE,
            'category': NotificationCategory.MUSIC,
            'title': '🆕 New Release',
            'message': 'New song from your favorite artist is out now!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.DAILY_MIX,
            'category': NotificationCategory.MUSIC,
            'title': '📝 Daily Mix',
            'message': 'Your daily music mix is ready to play!',
            'priority': NotificationPriority.LOW
        },
        {
            'type': NotificationType.MUSIC_SYNC,
            'category': NotificationCategory.MUSIC,
            'title': '🔄 Music Sync',
            'message': 'Your music library has been synced successfully',
            'priority': NotificationPriority.LOW
        },
        
        # Gamification
        {
            'type': NotificationType.LEVEL_UP,
            'category': NotificationCategory.GAMIFICATION,
            'title': '⬆️ Level Up!',
            'message': 'Congratulations! You reached Level 5 - Music Curator!',
            'priority': NotificationPriority.HIGH
        },
        {
            'type': NotificationType.ACHIEVEMENT_UNLOCKED,
            'category': NotificationCategory.GAMIFICATION,
            'title': '🏆 Achievement Unlocked!',
            'message': 'You unlocked "Pin Master" - Created 50 pins!',
            'priority': NotificationPriority.HIGH
        },
        {
            'type': NotificationType.CHALLENGE_COMPLETE,
            'category': NotificationCategory.GAMIFICATION,
            'title': '✅ Challenge Complete',
            'message': 'You completed the "Weekly Explorer" challenge!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.CHALLENGE_PROGRESS,
            'category': NotificationCategory.GAMIFICATION,
            'title': '📊 Challenge Progress',
            'message': 'You\'re halfway through the "Daily Discoverer" challenge!',
            'priority': NotificationPriority.LOW
        },
        {
            'type': NotificationType.CHALLENGE_AVAILABLE,
            'category': NotificationCategory.GAMIFICATION,
            'title': '🎯 New Challenge',
            'message': 'New weekly challenge available: "Music Explorer"!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.XP_EARNED,
            'category': NotificationCategory.GAMIFICATION,
            'title': '⭐ XP Earned',
            'message': 'You earned 50 XP for discovering a new area!',
            'priority': NotificationPriority.LOW
        },
        
        # Exploration & Discovery
        {
            'type': NotificationType.NEW_PINS_NEARBY,
            'category': NotificationCategory.EXPLORATION,
            'title': '🗺️ Fresh Pins Discovered!',
            'message': '7 fresh pins discovered near Central Park!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.NEW_AR_PINS,
            'category': NotificationCategory.EXPLORATION,
            'title': '🥽 New AR Pins',
            'message': 'New AR music experience available in Times Square!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.SEASONAL_DROP,
            'category': NotificationCategory.EXPLORATION,
            'title': '🎃 Seasonal Drop',
            'message': 'Halloween music pins are now available!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.EVENT_AVAILABLE,
            'category': NotificationCategory.EXPLORATION,
            'title': '🎪 Event Available',
            'message': 'Music festival pins are live at Coachella!',
            'priority': NotificationPriority.HIGH
        },
        {
            'type': NotificationType.TRENDING_IN_CITY,
            'category': NotificationCategory.EXPLORATION,
            'title': '🔥 Trending in City',
            'message': '"Blinding Lights" is trending in New York City!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.SEASONAL_EVENT,
            'category': NotificationCategory.EXPLORATION,
            'title': '🎄 Seasonal Event',
            'message': 'Christmas music event is now live in your area!',
            'priority': NotificationPriority.MEDIUM
        },
        
        # Collections & Playlists
        {
            'type': NotificationType.COLLECTION_UPDATE,
            'category': NotificationCategory.COLLECTION,
            'title': '📚 Collection Update',
            'message': 'Your "Favorites" collection has new pins!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.COLLECTION_MILESTONE,
            'category': NotificationCategory.COLLECTION,
            'title': '🎯 Collection Milestone',
            'message': 'Your collection reached 100 pins!',
            'priority': NotificationPriority.HIGH
        },
        {
            'type': NotificationType.COLLABORATIVE_UPDATE,
            'category': NotificationCategory.COLLECTION,
            'title': '👥 Collaborative Update',
            'message': 'Sarah added a new pin to your shared collection!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.PLAYLIST_SHARED,
            'category': NotificationCategory.COLLECTION,
            'title': '🎵 Playlist Shared',
            'message': 'Mike shared a playlist with you: "Road Trip Vibes"',
            'priority': NotificationPriority.MEDIUM
        },

        # Customization
        {
            'type': NotificationType.SKIN_UNLOCKED,
            'category': NotificationCategory.CUSTOMIZATION,
            'title': '🎨 Skin Unlocked!',
            'message': 'You unlocked the "Golden Note" pin skin!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.LIMITED_SKIN_AVAILABLE,
            'category': NotificationCategory.CUSTOMIZATION,
            'title': '⏰ Limited Skin Available',
            'message': 'Exclusive "Neon Beats" skin available for 24 hours!',
            'priority': NotificationPriority.HIGH
        },
        {
            'type': NotificationType.CUSTOMIZATION_REMINDER,
            'category': NotificationCategory.CUSTOMIZATION,
            'title': '🎨 Customization Reminder',
            'message': 'Don\'t forget to customize your pin with new skins!',
            'priority': NotificationPriority.LOW
        },
        
        # General
        {
            'type': NotificationType.GENERAL,
            'category': NotificationCategory.GENERAL,
            'title': '📢 General Notification',
            'message': 'Welcome to the BOP Maps notification test!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.SYSTEM_UPDATE,
            'category': NotificationCategory.GENERAL,
            'title': '🔄 System Update',
            'message': 'BOP Maps has been updated with new features!',
            'priority': NotificationPriority.LOW
        },
        {
            'type': NotificationType.WELCOME_MESSAGE,
            'category': NotificationCategory.GENERAL,
            'title': '👋 Welcome!',
            'message': 'Welcome to BOP Maps! Start exploring music around you!',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'type': NotificationType.RETENTION_REMINDER,
            'category': NotificationCategory.GENERAL,
            'title': '👋 Come Back!',
            'message': 'We miss you! Check out the new pins in your area!',
            'priority': NotificationPriority.LOW
        },
        {
            'type': NotificationType.UNREAD_REMINDER,
            'category': NotificationCategory.GENERAL,
            'title': '📬 Unread Notifications',
            'message': 'You have 3 unread notifications waiting for you!',
            'priority': NotificationPriority.LOW
        },
        {
            'type': NotificationType.WEEKLY_DIGEST,
            'category': NotificationCategory.SOCIAL,
            'title': '📊 Weekly Digest',
            'message': 'Your weekly activity summary is ready!',
            'priority': NotificationPriority.LOW
        }
    ]
    
    print(f"📋 Testing {len(test_notifications)} notification types...\n")
    
    success_count = 0
    failed_count = 0
    
    for i, notif_data in enumerate(test_notifications, 1):
        try:
            print(f"[{i:2d}/{len(test_notifications)}] Testing {notif_data['type']}...")
            
            notification = notification_manager.create_notification(
                recipient=user,
                notification_type=notif_data['type'],
                category=notif_data['category'],
                priority=notif_data['priority'],
                title=notif_data['title'],
                message=notif_data['message'],
                action_data={'test': True, 'timestamp': datetime.now().isoformat()}
            )
            
            if notification:
                print(f"    ✅ Created notification ID: {notification.id}")
                success_count += 1
            else:
                print(f"    ❌ Failed to create notification")
                failed_count += 1
                
            # Small delay between notifications to avoid overwhelming
            time.sleep(1)
            
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            failed_count += 1
    
    print(f"\n📊 Test Results:")
    print(f"✅ Successful: {success_count}")
    print(f"❌ Failed: {failed_count}")
    print(f"📱 Total notifications sent: {success_count}")
    
    if success_count > 0:
        print(f"\n🎉 Check your phone for {success_count} test notifications!")
        print("📱 Make sure you have the BOP Maps app installed and notifications enabled.")
    
    return success_count, failed_count

if __name__ == "__main__":
    print("🧪 BOP Maps Notification Test Suite")
    print("=" * 50)
    test_all_notifications()
